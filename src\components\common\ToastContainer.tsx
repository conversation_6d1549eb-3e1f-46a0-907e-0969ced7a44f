import React, { useState, useEffect, useCallback } from 'react';
import Toast, { ToastType } from './Toast';

export interface ToastItem {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
}

interface ToastContainerProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  maxToasts?: number;
}

/**
 * Container component for managing multiple toast notifications
 */
const ToastContainer: React.FC<ToastContainerProps> = ({
  position = 'top-right',
  maxToasts = 5
}) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  
  // Remove a toast by ID
  const removeToast = useCallback((id: string) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  }, []);
  
  // Add a new toast
  const addToast = useCallback((toast: Omit<ToastItem, 'id'>) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newToast = { ...toast, id };
    
    setToasts(prevToasts => {
      // If we've reached the maximum number of toasts, remove the oldest one
      if (prevToasts.length >= maxToasts) {
        return [...prevToasts.slice(1), newToast];
      }
      return [...prevToasts, newToast];
    });
    
    return id;
  }, [maxToasts]);
  
  // Expose the addToast and removeToast functions to the window for global access
  useEffect(() => {
    // @ts-expect-error TS2339: Property 'toastManager' does not exist on type 'Window & typeof globalThis'.
    window.toastManager = {
      addToast,
      removeToast
    };
    
    return () => {
      // @ts-expect-error TS2339: Property 'toastManager' does not exist on type 'Window & typeof globalThis'.
      delete window.toastManager;
    };
  }, [addToast, removeToast]);
  
  return (
    <div className={`toast-container toast-${position}`}>
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  );
};

export default ToastContainer;