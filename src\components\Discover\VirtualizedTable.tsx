import React, { useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { LogEntry } from '../../types/discover';

interface VirtualizedTableProps {
  data: LogEntry[];
  selectedFields: string[];
  formatFieldValue: (value: unknown, fieldName: string) => string;
  getFieldValue: (log: LogEntry, fieldPath: string) => unknown;
  onRowExpand?: (logId: string) => void;
  expandedRows?: Record<string, boolean>;
  className?: string;
  rowHeight?: number;
  headerHeight?: number;
}

/**
 * A virtualized table component for efficiently rendering large datasets
 */
const VirtualizedTable: React.FC<VirtualizedTableProps> = ({
  data,
  selectedFields,
  formatFieldValue,
  getFieldValue,
  onRowExpand,
  expandedRows = {},
  className = '',
  rowHeight = 40,
  headerHeight = 40
}) => {
  // Memoize field widths calculation
  const fieldWidths = useMemo(() => {
    const totalWidth = 100;
    const expandButtonWidth = onRowExpand ? 5 : 0;
    const availableWidth = totalWidth - expandButtonWidth;
    const fieldWidth = availableWidth / Math.max(selectedFields.length, 1);
    
    return {
      expandButton: expandButtonWidth,
      fields: selectedFields.map(() => fieldWidth)
    };
  }, [selectedFields, onRowExpand]);
  
  // Render table header
  const TableHeader = useCallback(() => {
    return (
      <div 
        className="virtualized-table-header" 
        style={{ 
          display: 'flex', 
          height: `${headerHeight}px`,
          borderBottom: '1px solid #e0e0e0'
        }}
      >
        {onRowExpand && (
          <div 
            className="header-cell expand-column" 
            style={{ width: `${fieldWidths.expandButton}%` }}
          ></div>
        )}
        
        {selectedFields.map((field, index) => (
          <div 
            key={field} 
            className="header-cell field-column" 
            style={{ 
              width: `${fieldWidths.fields[index]}%`,
              padding: '8px',
              fontWeight: 'bold',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {field}
          </div>
        ))}
      </div>
    );
  }, [selectedFields, fieldWidths, headerHeight, onRowExpand]);
  
  // Row renderer for virtualized list
  const Row = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const log = data[index];
    if (!log) return null;
    
    return (
      <div 
        className={`virtualized-table-row ${expandedRows[log.id] ? 'expanded' : ''}`}
        style={{ 
          ...style, 
          display: 'flex',
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: index % 2 === 0 ? '#ffffff' : '#f9f9f9'
        }}
      >
        {onRowExpand && (
          <div 
            className="row-cell expand-cell" 
            style={{ 
              width: `${fieldWidths.expandButton}%`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <button
              onClick={() => onRowExpand(log.id)}
              className="expand-button"
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px'
              }}
            >
              {expandedRows[log.id] ? '▼' : '▶'}
            </button>
          </div>
        )}
        
        {selectedFields.map((field, fieldIndex) => (
          <div 
            key={`${log.id}-${field}`} 
            className="row-cell field-cell" 
            style={{ 
              width: `${fieldWidths.fields[fieldIndex]}%`,
              padding: '8px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {formatFieldValue(getFieldValue(log, field), field)}
          </div>
        ))}
      </div>
    );
  }, [data, selectedFields, fieldWidths, expandedRows, onRowExpand, formatFieldValue, getFieldValue]);
  
  return (
    <div className={`virtualized-table ${className}`} style={{ height: '100%', width: '100%' }}>
      <TableHeader />
      
      <div style={{ height: `calc(100% - ${headerHeight}px)` }}>
        <AutoSizer>
          {({ height, width }) => (
            <List
              height={height}
              itemCount={data.length}
              itemSize={rowHeight}
              width={width}
            >
              {Row}
            </List>
          )}
        </AutoSizer>
      </div>
    </div>
  );
};

export default React.memo(VirtualizedTable);