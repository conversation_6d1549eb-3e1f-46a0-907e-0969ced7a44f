/**
 * Utility functions for Configuration Assessment feature
 */

import { 
  ConfigurationEntry, 
  ConfigurationFilter, 
  ConfigurationSearchParams,
  ConfigurationSort,
  SeverityLevel,
  SeverityMapping
} from '../types/configuration';

/**
 * Severity level mappings based on score
 */
const SEVERITY_MAPPINGS: SeverityMapping[] = [
  { min: 0, max: 3.9, level: SeverityLevel.LOW },
  { min: 4, max: 6.9, level: SeverityLevel.MEDIUM },
  { min: 7, max: 8.9, level: SeverityLevel.HIGH },
  { min: 9, max: 10, level: SeverityLevel.CRITICAL },
];

/**
 * Get severity level based on score
 * @param score Numeric score (0-10)
 * @returns Severity level
 */
export const getSeverityFromScore = (score: number): SeverityLevel => {
  const mapping = SEVERITY_MAPPINGS.find(m => score >= m.min && score <= m.max);
  return mapping ? mapping.level : SeverityLevel.LOW;
};

/**
 * Filter configuration entries based on search parameters
 * @param entries Array of configuration entries
 * @param searchParams Search parameters
 * @returns Filtered array of configuration entries
 */
export const filterConfigurationEntries = (
  entries: ConfigurationEntry[],
  searchParams?: ConfigurationSearchParams
): ConfigurationEntry[] => {
  // Early termination for empty search params
  if (!searchParams) {
    return entries;
  }

  // Early termination for empty entries
  if (!entries || entries.length === 0) {
    return [];
  }

  // Optimize by avoiding unnecessary array copy if no filters are applied
  const hasQuery = !!searchParams.query;
  const hasFilters = !!(searchParams.filters && searchParams.filters.length > 0);
  const hasTimeRange = !!searchParams.timeRange;
  
  // If no filtering is needed, skip to sorting/pagination
  if (!hasQuery && !hasFilters && !hasTimeRange && !searchParams.sort && 
      (searchParams.page === undefined || searchParams.pageSize === undefined)) {
    return entries;
  }

  // Create a single filtering function that combines all conditions
  // This avoids multiple iterations over the data
  const filterFn = (entry: ConfigurationEntry): boolean => {
    // Apply time range filter first (fastest check)
    if (hasTimeRange) {
      const entryTime = entry.timestamp.getTime();
      const startTime = searchParams.timeRange!.start.getTime();
      const endTime = searchParams.timeRange!.end.getTime();
      
      if (entryTime < startTime || entryTime > endTime) {
        return false;
      }
    }

    // Apply text search (second fastest)
    if (hasQuery) {
      const query = searchParams.query!.toLowerCase();
      
      // Use a single boolean expression with short-circuit evaluation
      const matchesQuery = 
        entry.rule.description.toLowerCase().includes(query) ||
        entry.check.title.toLowerCase().includes(query) ||
        entry.check.description.toLowerCase().includes(query) ||
        entry.component.toLowerCase().includes(query) ||
        entry.configuration.toLowerCase().includes(query);
      
      if (!matchesQuery) {
        return false;
      }
    }

    // Apply filters (most complex check, do last)
    if (hasFilters) {
      // Short-circuit as soon as any filter fails
      for (const filter of searchParams.filters!) {
        if (!applyFilter(entry, filter)) {
          return false;
        }
      }
    }

    // Entry passed all filters
    return true;
  };

  // Apply the combined filter in a single pass
  let filteredEntries = entries.filter(filterFn);

  // Apply sorting if needed
  if (searchParams.sort) {
    filteredEntries = sortConfigurationEntries(filteredEntries, searchParams.sort);
  }

  // Apply pagination if needed
  if (searchParams.page !== undefined && searchParams.pageSize !== undefined) {
    const startIndex = searchParams.page * searchParams.pageSize;
    // Avoid unnecessary slicing if the pagination would return the entire array
    if (startIndex === 0 && searchParams.pageSize >= filteredEntries.length) {
      return filteredEntries;
    }
    return filteredEntries.slice(
      startIndex,
      startIndex + searchParams.pageSize
    );
  }

  return filteredEntries;
};

/**
 * Cache for field paths to optimize repeated access
 */
const fieldPathCache: Record<string, string[]> = {};

/**
 * Get field value using optimized path access
 * @param entry Configuration entry
 * @param field Field path string
 * @returns Field value
 */
const getFieldValue = (entry: ConfigurationEntry, field: string | keyof ConfigurationEntry): unknown => {
  // Handle direct property access for better performance
  if (typeof field === 'string' && field.indexOf('.') === -1) {
    return entry[field as keyof ConfigurationEntry];
  }
  
  // Use cached path array if available
  const fieldString = field.toString();
  const fieldPath = fieldPathCache[fieldString] || (fieldPathCache[fieldString] = fieldString.split('.'));
  
  let value: unknown = entry;
  const pathLength = fieldPath.length;
  
  // Unrolled loop for common path lengths (optimization)
  if (pathLength === 1) {
    return entry[fieldPath[0] as keyof ConfigurationEntry];
  } else if (pathLength === 2) {
    const obj = entry[fieldPath[0] as keyof ConfigurationEntry];
    return obj && typeof obj === 'object' ? obj[fieldPath[1]] : undefined;
  } else if (pathLength === 3) {
    const obj1 = entry[fieldPath[0] as keyof ConfigurationEntry];
    if (obj1 && typeof obj1 === 'object') {
      const obj2 = obj1[fieldPath[1]];
      return obj2 && typeof obj2 === 'object' ? obj2[fieldPath[2]] : undefined;
    }
    return undefined;
  }
  
  // General case for deeper paths
  for (let i = 0; i < pathLength; i++) {
    if (value === undefined || value === null) {
      return undefined;
    }
    value = value[fieldPath[i]];
  }
  
  return value;
};

/**
 * Apply a single filter to a configuration entry
 * @param entry Configuration entry
 * @param filter Filter to apply
 * @returns Boolean indicating if entry passes filter
 */
const applyFilter = (entry: ConfigurationEntry, filter: ConfigurationFilter): boolean => {
  // Get the field value using optimized accessor
  const value = getFieldValue(entry, filter.field);
  
  // Early termination for undefined/null values
  if (value === undefined || value === null) {
    // Special case for existence operators
    if (filter.operator === 'not_in') {
      return true; // null is not in any array
    }
    return false;
  }

  // Optimize string comparison by pre-computing lowercase values
  let valueStr: string | undefined;
  let filterValueStr: string | undefined;
  
  // Type-specific optimizations
  switch (filter.operator) {
    case 'eq':
      return value === filter.value;
    case 'neq':
      return value !== filter.value;
    case 'gt':
      // Type coercion for numeric comparison
      return typeof value === 'number' ? 
        value > Number(filter.value) : 
        String(value) > String(filter.value);
    case 'gte':
      return typeof value === 'number' ? 
        value >= Number(filter.value) : 
        String(value) >= String(filter.value);
    case 'lt':
      return typeof value === 'number' ? 
        value < Number(filter.value) : 
        String(value) < String(filter.value);
    case 'lte':
      return typeof value === 'number' ? 
        value <= Number(filter.value) : 
        String(value) <= String(filter.value);
    case 'contains':
      if (typeof value !== 'string') {
        valueStr = String(value).toLowerCase();
      } else {
        valueStr = value.toLowerCase();
      }
      
      if (typeof filter.value !== 'string') {
        filterValueStr = String(filter.value).toLowerCase();
      } else {
        filterValueStr = (filter.value as string).toLowerCase();
      }
      
      return valueStr.includes(filterValueStr);
    case 'in':
      return Array.isArray(filter.value) && filter.value.includes(value);
    case 'not_in':
      return Array.isArray(filter.value) && !filter.value.includes(value);
    default:
      return false;
  }
};

/**
 * Sort configuration entries based on sort parameters
 * @param entries Array of configuration entries
 * @param sort Sort parameters
 * @returns Sorted array of configuration entries
 */
export const sortConfigurationEntries = (
  entries: ConfigurationEntry[],
  sort: ConfigurationSort
): ConfigurationEntry[] => {
  // Early termination for empty arrays
  if (!entries || entries.length <= 1) {
    return entries;
  }

  // Cache field path to avoid repeated splitting
  const fieldString = sort.field.toString();
  // Unused variable removed
  fieldPathCache[fieldString] = fieldPathCache[fieldString] || fieldString.split('.');
  const isAscending = sort.direction === 'asc';
  
  // Determine the type of the field for optimized comparison
  const sampleValue = getFieldValue(entries[0], fieldString);
  const valueType = typeof sampleValue;
  const isDate = sampleValue instanceof Date;
  const isString = valueType === 'string';
  const isNumber = valueType === 'number';
  
  // Create a specialized comparison function based on field type
  // This avoids type checking in every comparison
  let compareFunction: (a: ConfigurationEntry, b: ConfigurationEntry) => number;
  
  if (isDate) {
    // Optimized date comparison
    compareFunction = (a: ConfigurationEntry, b: ConfigurationEntry): number => {
      const valueA = getFieldValue(a, fieldString) as Date | undefined;
      const valueB = getFieldValue(b, fieldString) as Date | undefined;
      
      // Handle undefined or null values
      if (valueA === undefined || valueA === null) {
        return isAscending ? -1 : 1;
      }
      if (valueB === undefined || valueB === null) {
        return isAscending ? 1 : -1;
      }
      
      const timeA = valueA.getTime();
      const timeB = valueB.getTime();
      
      return isAscending ? timeA - timeB : timeB - timeA;
    };
  } else if (isString) {
    // Optimized string comparison with locale
    compareFunction = (a: ConfigurationEntry, b: ConfigurationEntry): number => {
      const valueA = getFieldValue(a, fieldString) as string | undefined;
      const valueB = getFieldValue(b, fieldString) as string | undefined;
      
      // Handle undefined or null values
      if (valueA === undefined || valueA === null) {
        return isAscending ? -1 : 1;
      }
      if (valueB === undefined || valueB === null) {
        return isAscending ? 1 : -1;
      }
      
      return isAscending ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
    };
  } else if (isNumber) {
    // Optimized number comparison
    compareFunction = (a: ConfigurationEntry, b: ConfigurationEntry): number => {
      const valueA = getFieldValue(a, fieldString) as number | undefined;
      const valueB = getFieldValue(b, fieldString) as number | undefined;
      
      // Handle undefined or null values
      if (valueA === undefined || valueA === null) {
        return isAscending ? -1 : 1;
      }
      if (valueB === undefined || valueB === null) {
        return isAscending ? 1 : -1;
      }
      
      return isAscending ? valueA - valueB : valueB - valueA;
    };
  } else {
    // Generic comparison for other types
    compareFunction = (a: ConfigurationEntry, b: ConfigurationEntry): number => {
      const valueA = getFieldValue(a, fieldString);
      const valueB = getFieldValue(b, fieldString);
      
      // Handle undefined or null values
      if (valueA === undefined || valueA === null) {
        return isAscending ? -1 : 1;
      }
      if (valueB === undefined || valueB === null) {
        return isAscending ? 1 : -1;
      }
      
      // Compare based on value type
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return isAscending ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
      } else {
        // Convert to string for stable comparison if types don't match
        const strA = String(valueA);
        const strB = String(valueB);
        return isAscending ? strA.localeCompare(strB) : strB.localeCompare(strA);
      }
    };
  }
  
  // Use a stable sort algorithm
  return stableSort([...entries], compareFunction);
};

/**
 * Implements a stable sort algorithm
 * This ensures that equal elements maintain their relative order
 */
function stableSort<T>(array: T[], compare: (a: T, b: T) => number): T[] {
  // For small arrays, use the built-in sort (more efficient)
  if (array.length <= 1000) {
    return array.sort(compare);
  }
  
  // For larger arrays, use a stable sort implementation
  return array
    .map((item, index) => ({ item, index }))
    .sort((a, b) => {
      const order = compare(a.item, b.item);
      // If items are equal, maintain original order
      return order !== 0 ? order : a.index - b.index;
    })
    .map(({ item }) => item);
}

/**
 * Group configuration entries by a specific field
 * @param entries Array of configuration entries
 * @param field Field to group by
 * @returns Object with groups of entries
 */
export const groupConfigurationEntries = (
  entries: ConfigurationEntry[],
  field: keyof ConfigurationEntry | string
): Record<string, ConfigurationEntry[]> => {
  const groups: Record<string, ConfigurationEntry[]> = {};

  entries.forEach(entry => {
    // Handle nested fields with dot notation
    const fieldPath = field.toString().split('.');
    let value: Record<string, unknown> = entry as Record<string, unknown>;
    
    for (const part of fieldPath) {
      if (value === undefined || value === null) {
        value = undefined; // Ensure value is undefined if any part of the path is null/undefined
        break;
      }
      value = value[part];
    }

    const groupKey = String(value);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(entry);
  });

  return groups;
};

/**
 * Count configuration entries by result status
 * @param entries Array of configuration entries
 * @returns Object with counts by status
 */
export const countByResultStatus = (
  entries: ConfigurationEntry[]
): Record<string, number> => {
  const counts: Record<string, number> = {
    passed: 0,
    failed: 0,
    not_applicable: 0
  };

  entries.forEach(entry => {
    if (counts[entry.result] !== undefined) {
      counts[entry.result]++;
    }
  });

  return counts;
};

/**
 * Count configuration entries by compliance standard
 * @param entries Array of configuration entries
 * @returns Object with counts by compliance standard
 */
export const countByComplianceStandard = (
  entries: ConfigurationEntry[]
): Record<string, number> => {
  const counts: Record<string, number> = {};

  entries.forEach(entry => {
    entry.check.compliance.forEach(standard => {
      if (!counts[standard]) {
        counts[standard] = 0;
      }
      counts[standard]++;
    });
  });

  return counts;
};

/**
 * Calculate compliance score based on passed/failed checks
 * @param entries Array of configuration entries
 * @returns Compliance score as percentage
 */
export const calculateComplianceScore = (entries: ConfigurationEntry[]): number => {
  if (entries.length === 0) {
    return 100; // Default to 100% if no entries
  }

  const relevantEntries = entries.filter(entry => entry.result !== 'not_applicable');
  if (relevantEntries.length === 0) {
    return 100; // Default to 100% if all entries are not applicable
  }

  const passedCount = relevantEntries.filter(entry => entry.result === 'passed').length;
  return Math.round((passedCount / relevantEntries.length) * 100);
};

/**
 * Format configuration entry for display
 * @param entry Configuration entry
 * @returns Formatted entry for display
 */
export const formatConfigurationEntryForDisplay = (
  entry: ConfigurationEntry
): Record<string, unknown> => {
  return {
    id: entry.id,
    timestamp: entry.timestamp.toLocaleString(),
    agent: `${entry.agent.name} (${entry.agent.ip})`,
    rule_id: entry.rule.id,
    rule_description: entry.rule.description,
    check_title: entry.check.title,
    result: entry.result,
    severity: getSeverityFromScore(entry.score),
    score: entry.score,
    component: entry.component,
    compliance: entry.check.compliance.join(', ')
  };
};

/**
 * Convert configuration entries to CSV format
 * @param entries Array of configuration entries
 * @returns CSV string
 */
export const convertConfigurationEntriesToCSV = (
  entries: ConfigurationEntry[]
): string => {
  if (entries.length === 0) {
    return '';
  }

  // Format entries for CSV
  const formattedEntries = entries.map(formatConfigurationEntryForDisplay);
  
  // Get headers from first entry
  const headers = Object.keys(formattedEntries[0]);
  
  // Create CSV header row
  const csvContent = [
    headers.join(','),
    ...formattedEntries.map(entry => 
      headers.map(header => {
        const value = entry[header];
        // Handle values that need quotes (strings with commas, etc.)
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');
  
  return csvContent;
};

/**
 * Convert configuration entries to JSON format
 * @param entries Array of configuration entries
 * @returns JSON string
 */
export const convertConfigurationEntriesToJSON = (
  entries: ConfigurationEntry[]
): string => {
  return JSON.stringify(entries.map(formatConfigurationEntryForDisplay), null, 2);
};