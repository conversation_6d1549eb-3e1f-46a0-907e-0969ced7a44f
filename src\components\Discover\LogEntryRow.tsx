import React from 'react';
import { LogEntry } from '../../types/discover';
import { useDiscoverLogs } from '../../hooks';

interface LogEntryRowProps {
  log: LogEntry;
  isExpanded: boolean;
  onToggleExpand: () => void;
  selectedFields: string[];
  columnWidths?: Record<string, number>;
}

/**
 * Component for displaying a single log entry row in the table
 */
const LogEntryRow: React.FC<LogEntryRowProps> = ({ 
  log, 
  isExpanded, 
  onToggleExpand,
  selectedFields,
  columnWidths = {}
}) => {
  const { getFieldValue, formatFieldValue, getLogLevelColor } = useDiscoverLogs();
  
  return (
    <div 
      role="row"
      aria-expanded={isExpanded}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onToggleExpand();
        }
      }}
      style={{
        display: 'flex',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        padding: '8px 16px',
        color: 'white',
        fontSize: '14px',
        cursor: 'pointer',
        background: isExpanded 
          ? 'rgba(0, 229, 255, 0.05)' 
          : 'transparent',
        transition: 'background-color 0.2s',
        alignItems: 'center',
        outline: 'none',
      }}
      onClick={onToggleExpand}
    >
      <div style={{ width: '30px', display: 'flex', alignItems: 'center' }}>
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="#00e5ff" 
          strokeWidth="2"
          style={{
            transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s',
          }}
        >
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </div>
      
      {selectedFields.map(field => {
        const value = getFieldValue(log, field);
        
        // Special styling for level field
        if (field === 'level') {
          const levelColor = getLogLevelColor(value as string);
          return (
            <div 
              key={field}
              style={{ 
                flexGrow: 1,
                padding: '0 8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                color: levelColor,
              }}
            >
              {formatFieldValue(value)}
            </div>
          );
        }
        
        return (
          <div 
            key={field}
            style={{ 
              flexGrow: columnWidths[field] ? 0 : (field === 'message' ? 3 : 1),
              padding: '0 8px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              minWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
            }}
          >
            {formatFieldValue(value)}
          </div>
        );
      })}
    </div>
  );
};

export default LogEntryRow;