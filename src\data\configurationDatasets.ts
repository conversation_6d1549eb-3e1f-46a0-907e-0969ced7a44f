/**
 * Configuration Assessment Sample Datasets
 * 
 * This file exports various pre-generated datasets for configuration assessment data
 * with different sizes and result distributions for testing and development purposes.
 */

import { ConfigurationDataGenerator } from './sampleConfigurationData';
import { ConfigurationEntry } from '../types/configuration';

/**
 * Standard dataset sizes
 */
export const DATASET_SIZES = {
  TINY: 10,
  SMALL: 50,
  MEDIUM: 100,
  LARGE: 500,
  EXTRA_LARGE: 1000
};

/**
 * Result distribution scenarios
 */
export const RESULT_SCENARIOS = {
  DEFAULT: { passed: 60, failed: 30, notApplicable: 10 },
  ALL_PASSED: { passed: 100, failed: 0, notApplicable: 0 },
  ALL_FAILED: { passed: 0, failed: 100, notApplicable: 0 },
  MIXED: { passed: 50, failed: 40, notApplicable: 10 },
  MOSTLY_PASSED: { passed: 80, failed: 15, notApplicable: 5 },
  MOSTLY_FAILED: { passed: 15, failed: 80, notApplicable: 5 },
  HIGH_NOT_APPLICABLE: { passed: 45, failed: 25, notApplicable: 30 }
};

/**
 * Generate a dataset with specific size and result distribution
 * @param size Number of entries
 * @param scenario Result distribution scenario
 * @returns Array of configuration entries
 */
export function generateDataset(
  size: number = DATASET_SIZES.MEDIUM,
  scenario = RESULT_SCENARIOS.DEFAULT
): ConfigurationEntry[] {
  return ConfigurationDataGenerator.generateDataset(
    size,
    scenario.passed,
    scenario.failed,
    scenario.notApplicable
  );
}

// Standard datasets with default distribution
export const tinyDataset = generateDataset(DATASET_SIZES.TINY);
export const smallDataset = generateDataset(DATASET_SIZES.SMALL);
export const mediumDataset = generateDataset(DATASET_SIZES.MEDIUM);
export const largeDataset = generateDataset(DATASET_SIZES.LARGE);
export const extraLargeDataset = generateDataset(DATASET_SIZES.EXTRA_LARGE);

// Datasets with different result distributions (medium size)
export const allPassedDataset = generateDataset(DATASET_SIZES.MEDIUM, RESULT_SCENARIOS.ALL_PASSED);
export const allFailedDataset = generateDataset(DATASET_SIZES.MEDIUM, RESULT_SCENARIOS.ALL_FAILED);
export const mixedDataset = generateDataset(DATASET_SIZES.MEDIUM, RESULT_SCENARIOS.MIXED);
export const mostlyPassedDataset = generateDataset(DATASET_SIZES.MEDIUM, RESULT_SCENARIOS.MOSTLY_PASSED);
export const mostlyFailedDataset = generateDataset(DATASET_SIZES.MEDIUM, RESULT_SCENARIOS.MOSTLY_FAILED);
export const highNotApplicableDataset = generateDataset(DATASET_SIZES.MEDIUM, RESULT_SCENARIOS.HIGH_NOT_APPLICABLE);

/**
 * Default dataset for use in the application
 * This is the dataset that will be used by default in the ConfigurationAssessmentContext
 */
export const defaultDataset = mediumDataset;

/**
 * Export a dataset to JSON format
 * @param dataset Configuration entries to export
 * @returns JSON string representation of the dataset
 */
export function exportDatasetToJSON(dataset: ConfigurationEntry[]): string {
  return JSON.stringify(dataset, (key, value) => {
    // Convert Date objects to ISO strings for proper serialization
    if (value instanceof Date) {
      return value.toISOString();
    }
    return value;
  }, 2);
}

/**
 * Export a dataset to CSV format
 * @param dataset Configuration entries to export
 * @returns CSV string representation of the dataset
 */
export function exportDatasetToCSV(dataset: ConfigurationEntry[]): string {
  if (dataset.length === 0) {
    return '';
  }

  // Define the fields to include in the CSV
  const fields = [
    'id',
    'timestamp',
    'agent.id',
    'agent.name',
    'agent.ip',
    'rule.id',
    'rule.description',
    'rule.level',
    'check.title',
    'check.description',
    'check.remediation',
    'result',
    'score',
    'component',
    'configuration'
  ];

  // Create header row
  const header = fields.join(',');

  // Create data rows
  const rows = dataset.map(entry => {
    return fields.map(field => {
      // Handle nested fields with dot notation
      const parts = field.split('.');
      let value: unknown = entry;
      
      for (const part of parts) {
        if (value === undefined || value === null) {
          return '';
        }
        value = value[part];
      }

      // Format value for CSV
      if (value === undefined || value === null) {
        return '';
      } else if (value instanceof Date) {
        return value.toISOString();
      } else if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      } else if (Array.isArray(value)) {
        return `"${value.join(', ')}"`;
      } else {
        return String(value);
      }
    }).join(',');
  });

  // Combine header and rows
  return [header, ...rows].join('\n');
}