import { ConfigurationAssessmentState, ConfigurationAssessmentAction } from '../types/configurationAssessment';

export const getInitialState = (): ConfigurationAssessmentState => ({
  searchQuery: '',
  timeRange: {
    start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
    end: new Date(),
    preset: 'last-24h',
  },
  selectedFields: ['timestamp', 'rule.description', 'check.title', 'result', 'score'],
  appliedFilters: [],
  configData: [],
  filteredData: [],
  histogramData: [],
  isLoading: false,
  autoRefresh: false,
  refreshInterval: 60000, // 1 minute
  pagination: {
    currentPage: 1,
    pageSize: 25,
    totalItems: 0,
  },
  error: null,
  isProcessing: false,
});

export function configurationAssessmentReducer(state: ConfigurationAssessmentState, action: ConfigurationAssessmentAction): ConfigurationAssessmentState {
  switch (action.type) {
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    case 'SET_TIME_RANGE':
      return { ...state, timeRange: action.payload };
    case 'ADD_FILTER':
      return { ...state, appliedFilters: [...state.appliedFilters, action.payload] };
    case 'REMOVE_FILTER':
      return { ...state, appliedFilters: state.appliedFilters.filter(filter => filter.field !== action.payload) };
    case 'TOGGLE_FIELD': {
      const fieldExists = state.selectedFields.includes(action.payload);
      const selectedFields = fieldExists
        ? state.selectedFields.filter(field => field !== action.payload)
        : [...state.selectedFields, action.payload];
      return { ...state, selectedFields };
    }
    case 'SET_CONFIG_DATA':
      return { ...state, configData: action.payload, filteredData: action.payload, pagination: { ...state.pagination, totalItems: action.payload.length }, isLoading: false, isProcessing: false };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_AUTO_REFRESH':
      return { ...state, autoRefresh: action.payload };
    case 'SET_REFRESH_INTERVAL':
      return { ...state, refreshInterval: action.payload };
    case 'SET_CURRENT_PAGE':
      return { ...state, pagination: { ...state.pagination, currentPage: action.payload } };
    case 'SET_PAGE_SIZE':
      return { ...state, pagination: { ...state.pagination, pageSize: action.payload, currentPage: 1 } };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false, isProcessing: false };
    case 'SET_PROCESSING_OPERATION':
      return { ...state, isProcessing: action.payload };
    default:
      return state;
  }
}