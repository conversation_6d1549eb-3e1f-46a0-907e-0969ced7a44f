import { DataPlugin } from '../data-plugin';
import { SearchServiceImpl } from '../services/search-service';
import { QueryServiceImpl } from '../services/query-service';
import { UiServiceImpl } from '../services/ui-service';
import { FieldFormatsServiceImpl } from '../services/field-formats-service';
import { AutocompleteServiceImpl } from '../services/autocomplete-service';
import { DataStorageImpl } from '../services/data-storage';
import { IndexPatternService } from '../services/index-pattern-service';

describe('DataPlugin', () => {
  let dataPlugin: DataPlugin;
  
  beforeEach(() => {
    // Reset the singleton instance before each test
    // @ts-expect-error: Accessing private static property for testing purposes.
    DataPlugin['instance'] = null;
    
    // Get a new instance
    dataPlugin = DataPlugin.getInstance();
  });
  
  it('should be a singleton', () => {
    // Get another instance
    const anotherInstance = DataPlugin.getInstance();
    
    // Check that both references point to the same instance
    expect(anotherInstance).toBe(dataPlugin);
  });
  
  it('should initialize successfully', async () => {
    // Initialize the plugin
    await dataPlugin.initialize();
    
    // Check that initialization was successful
    // @ts-expect-error - accessing private property for testing
    expect(dataPlugin.initialized).toBe(true);
    
    // Initialize again to test the early return
    await dataPlugin.initialize();
    
    // Should still be initialized
    // @ts-expect-error - accessing private property for testing
    expect(dataPlugin.initialized).toBe(true);
  });
  
  it('should provide access to services', () => {
    // Check that all services are available
    expect(dataPlugin.getSearchService()).toBeInstanceOf(SearchServiceImpl);
    expect(dataPlugin.getQueryService()).toBeInstanceOf(QueryServiceImpl);
    expect(dataPlugin.getUiService()).toBeInstanceOf(UiServiceImpl);
    expect(dataPlugin.getFieldFormatsService()).toBeInstanceOf(FieldFormatsServiceImpl);
    expect(dataPlugin.getAutocompleteService()).toBeInstanceOf(AutocompleteServiceImpl);
    expect(dataPlugin.getStorage()).toBeInstanceOf(DataStorageImpl);
    expect(dataPlugin.getIndexPatternService()).toBeInstanceOf(IndexPatternService);
  });
  
  it('should handle initialization errors', async () => {
    // Mock an error during initialization
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // @ts-expect-error TS2345: Argument of type 'DataPlugin' is not assignable to parameter of type 'object'.
    jest.spyOn(dataPlugin as DataPlugin, 'initializeServices').mockRejectedValue(new Error('Initialization error'));
    
    // Try to initialize
    await expect(dataPlugin.initialize()).rejects.toThrow('Initialization error');
    
    // Check that the plugin is not initialized
    // @ts-expect-error TS2341: Property 'initialized' is private and only accessible within class 'DataPlugin'.
    expect(dataPlugin.initialized).toBe(false);
    
    // Restore console.error
    jest.restoreAllMocks();
  });
});