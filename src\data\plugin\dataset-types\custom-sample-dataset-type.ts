import { DatasetTypeConfig, Dataset, DatasetField, DataStructure } from '../interfaces';

/**
 * Custom sample dataset type implementation
 * This implements a DatasetTypeConfig for sample data as required by task 6.1
 */
export const customSampleDatasetType: DatasetTypeConfig = {
  id: 'custom-sample-data',
  title: 'Custom Sample Data',
  meta: {
    icon: 'table', // This would be a component or icon reference in a real implementation
    tooltip: 'Custom sample data for demonstration of extension points',
    searchOnLoad: true,
    isFieldLoadAsync: true
  },
  
  /**
   * Converts a path to a dataset
   * @param path The path to convert
   * @returns The created dataset
   */
  toDataset: (path: DataStructure[]): Dataset => {
    if (!path || path.length === 0) {
      throw new Error('Invalid path');
    }
    
    const lastItem = path[path.length - 1];
    
    return {
      id: `custom-${lastItem.id}`,
      title: lastItem.title,
      type: 'custom-sample-data',
      timeFieldName: 'timestamp',
      language: 'simple-query'
    };
  },
  
  /**
   * Fetches data structure for the dataset
   * @param services Services needed for fetching
   * @param path The path to fetch
   * @returns The fetched data structure
   */
  fetch: async (services: unknown, path: DataStructure[]): Promise<DataStructure> => {
    // This implementation fetches custom sample data structures
    const lastItem = path.length > 0 ? path[path.length - 1] : null;
    
    if (!lastItem) {
      // Root level - return available datasets
      return {
        id: 'custom-root',
        title: 'Custom Sample Data',
        type: 'folder',
        children: [
          {
            id: 'transactions',
            title: 'Transactions',
            type: 'dataset'
          },
          {
            id: 'users',
            title: 'Users',
            type: 'dataset'
          },
          {
            id: 'products',
            title: 'Products',
            type: 'dataset'
          }
        ]
      };
    }
    
    // Return details for the specific dataset
    return {
      id: lastItem.id,
      title: lastItem.title,
      type: 'dataset',
      meta: {
        count: lastItem.id === 'transactions' ? 5000 : 
               lastItem.id === 'users' ? 1000 : 500,
        lastUpdated: new Date().toISOString(),
        description: `Custom sample ${lastItem.title.toLowerCase()} data`
      }
    };
  },
  
  /**
   * Fetches fields for a dataset
   * @param dataset The dataset to fetch fields for
   * @returns Array of dataset fields
   */
  fetchFields: async (dataset: Dataset): Promise<DatasetField[]> => {
    // Common fields across all datasets
    const commonFields: DatasetField[] = [
      {
        name: 'timestamp',
        type: 'date',
        searchable: true,
        aggregatable: true,
        format: {
          id: 'date',
          params: {
            pattern: 'YYYY-MM-DD HH:mm:ss'
          }
        }
      },
      {
        name: 'id',
        type: 'keyword',
        searchable: true,
        aggregatable: true
      }
    ];
    
    // Add specific fields based on dataset ID
    if (dataset.id.includes('transactions')) {
      return [
        ...commonFields,
        {
          name: 'amount',
          type: 'float',
          searchable: true,
          aggregatable: true,
          format: {
            id: 'number',
            params: {
              pattern: '$0,0.00'
            }
          }
        },
        {
          name: 'status',
          type: 'keyword',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'user_id',
          type: 'keyword',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'product_id',
          type: 'keyword',
          searchable: true,
          aggregatable: true
        }
      ];
    } else if (dataset.id.includes('users')) {
      return [
        ...commonFields,
        {
          name: 'name',
          type: 'text',
          searchable: true,
          aggregatable: false
        },
        {
          name: 'email',
          type: 'keyword',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'age',
          type: 'integer',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'active',
          type: 'boolean',
          searchable: true,
          aggregatable: true
        }
      ];
    } else if (dataset.id.includes('products')) {
      return [
        ...commonFields,
        {
          name: 'name',
          type: 'text',
          searchable: true,
          aggregatable: false
        },
        {
          name: 'price',
          type: 'float',
          searchable: true,
          aggregatable: true,
          format: {
            id: 'number',
            params: {
              pattern: '$0,0.00'
            }
          }
        },
        {
          name: 'category',
          type: 'keyword',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'in_stock',
          type: 'boolean',
          searchable: true,
          aggregatable: true
        }
      ];
    }
    
    return commonFields;
  },
  
  /**
   * Returns the supported query languages for this dataset type
   * @returns Array of supported language IDs
   */
  supportedLanguages: (): string[] => {
    return ['simple-query', 'kuery'];
  }
};