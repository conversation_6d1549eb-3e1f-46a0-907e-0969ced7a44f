import React, { create<PERSON>ontext, use<PERSON>ontext, ReactNode, useEffect, useState } from 'react';
import { DataPlugin } from '../data/plugin/data-plugin';
import { initializeDataPlugin } from '../data/plugin/initialize';

// Create context
const DataPluginContext = createContext<DataPlugin | undefined>(undefined);

// Provider component
interface DataPluginProviderProps {
  children: ReactNode;
}

// Export the provider component for use in the application
export const DataPluginProvider: React.FC<DataPluginProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Get the singleton instance of the DataPlugin
  const dataPlugin = DataPlugin.getInstance();
  
  // Initialize the DataPlugin and register extensions
  useEffect(() => {
    const initializePlugin = async () => {
      try {
        // Use the enhanced initialization function that also registers extensions
        await initializeDataPlugin();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize DataPlugin:', error);
        setError(error instanceof Error ? error : new Error('Unknown error during initialization'));
      }
    };
    
    initializePlugin();
  }, []);
  
  // Show error state if initialization failed
  if (error) {
    return (
      <div className="error-container">
        <h3>Failed to initialize data services</h3>
        <p>{error.message}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }
  
  // Only render children when the plugin is initialized
  if (!isInitialized) {
    return <div>Initializing data services...</div>;
  }
  
  return (
    <DataPluginContext.Provider value={dataPlugin}>
      {children}
    </DataPluginContext.Provider>
  );
};

// Custom hook for using the context
export const useDataPlugin = (): DataPlugin => {
  const context = useContext(DataPluginContext);
  
  if (context === undefined) {
    throw new Error('useDataPlugin must be used within a DataPluginProvider');
  }
  
  return context;
};