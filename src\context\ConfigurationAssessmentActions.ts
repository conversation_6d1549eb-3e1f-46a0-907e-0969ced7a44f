/**
 * Configuration Assessment Actions
 * 
 * This file contains action creators for the Configuration Assessment context.
 */

import { 
  ConfigurationAssessmentAction,
  ConfigurationAssessmentState
} from './ConfigurationAssessmentContext';
import { ConfigurationEntry, ConfigurationFilter, ConfigurationSort } from '../types/configuration';

/**
 * Action creator functions for Configuration Assessment operations
 */
export const configurationAssessmentActions = {
  /**
   * Set the search query for filtering configuration entries
   * @param query Search query string
   */
  setSearchQuery: (query: string): ConfigurationAssessmentAction => ({
    type: 'SET_SEARCH_QUERY',
    payload: query,
  }),

  /**
   * Set the time range for filtering configuration entries
   * @param start Start date
   * @param end End date
   * @param preset Optional preset identifier
   */
  setTimeRange: (
    start: Date,
    end: Date,
    preset?: string
  ): ConfigurationAssessmentAction => ({
    type: 'SET_TIME_RANGE',
    payload: { start, end, preset },
  }),

  /**
   * Add a filter to the applied filters
   * @param field Field to filter on
   * @param value Value to filter by
   * @param operator Filter operator
   */
  addFilter: (
    field: string,
    value: unknown,
    operator: 'eq' | 'neq' | 'contains' | 'in' | 'not_in' = 'eq'
  ): ConfigurationAssessmentAction => ({
    type: 'ADD_FILTER',
    payload: { field, value, operator },
  }),

  /**
   * Remove a filter by field name
   * @param field Field name of the filter to remove
   */
  removeFilter: (field: string): ConfigurationAssessmentAction => ({
    type: 'REMOVE_FILTER',
    payload: field,
  }),

  /**
   * Toggle a field in the selected fields list
   * @param field Field to toggle
   */
  toggleField: (field: string): ConfigurationAssessmentAction => ({
    type: 'TOGGLE_FIELD',
    payload: field,
  }),

  /**
   * Set auto-refresh enabled/disabled
   * @param enabled Whether auto-refresh is enabled
   */
  setAutoRefresh: (enabled: boolean): ConfigurationAssessmentAction => ({
    type: 'SET_AUTO_REFRESH',
    payload: enabled,
  }),

  /**
   * Set the auto-refresh interval
   * @param interval Refresh interval in milliseconds
   */
  setRefreshInterval: (interval: number): ConfigurationAssessmentAction => ({
    type: 'SET_REFRESH_INTERVAL',
    payload: interval,
  }),

  /**
   * Trigger a manual refresh of the data
   */
  refresh: (): ConfigurationAssessmentAction => ({
    type: 'SET_LOADING',
    payload: true
  }),

  /**
   * Set the current page for pagination
   * @param page Page number (0-based)
   */
  setCurrentPage: (page: number): ConfigurationAssessmentAction => ({
    type: 'SET_CURRENT_PAGE',
    payload: page,
  }),

  /**
   * Set the page size for pagination
   * @param size Number of items per page
   */
  setPageSize: (size: number): ConfigurationAssessmentAction => ({
    type: 'SET_PAGE_SIZE',
    payload: size,
  }),

  /**
   * Set the sort field and direction
   * @param field Field to sort by
   * @param direction Sort direction
   */
  setSort: (
    field: string,
    direction: 'asc' | 'desc'
  ): ConfigurationAssessmentAction => ({
    type: 'SET_SORT',
    payload: { field, direction },
  }),

  /**
   * Clear the current sort
   */
  clearSort: (): ConfigurationAssessmentAction => ({
    type: 'SET_SORT',
    payload: null,
  }),

  /**
   * Set the configuration data
   * @param data Array of configuration entries
   */
  setConfigData: (data: ConfigurationEntry[]): ConfigurationAssessmentAction => ({
    type: 'SET_CONFIG_DATA',
    payload: data,
  }),

  /**
   * Set the loading state
   * @param isLoading Whether the data is loading
   */
  setLoading: (isLoading: boolean): ConfigurationAssessmentAction => ({
    type: 'SET_LOADING',
    payload: isLoading,
  }),
  
  /**
   * Set the error state
   * @param error Error message or null if no error
   */
  setError: (error: string | null): ConfigurationAssessmentAction => ({
    type: 'SET_ERROR',
    payload: error,
  }),
  
  /**
   * Set the processing operation state
   * @param isProcessing Whether an operation is in progress
   */
  setProcessingOperation: (isProcessing: boolean): ConfigurationAssessmentAction => ({
    type: 'SET_PROCESSING_OPERATION',
    payload: isProcessing,
  }),
};

/**
 * Helper function to create a filter
 * @param field Field to filter on
 * @param value Value to filter by
 * @param operator Filter operator
 * @returns ConfigurationFilter object
 */
export function createFilter(
  field: string,
  value: unknown,
  operator: 'eq' | 'neq' | 'contains' | 'in' | 'not_in' = 'eq'
): ConfigurationFilter {
  return {
    field,
    value,
    operator,
  };
}

/**
 * Helper function to create a sort configuration
 * @param field Field to sort by
 * @param direction Sort direction
 * @returns ConfigurationSort object
 */
export function createSort(
  field: string,
  direction: 'asc' | 'desc' = 'asc'
): ConfigurationSort {
  return {
    field,
    direction,
  };
}

/**
 * Helper function to get the current page of data
 * @param state Current state
 * @returns Current page of data
 */
export function getCurrentPageData(state: ConfigurationAssessmentState): ConfigurationEntry[] {
  const { filteredData, pagination } = state;
  const { currentPage, pageSize } = pagination;
  
  const startIndex = currentPage * pageSize;
  return filteredData.slice(startIndex, startIndex + pageSize);
}

/**
 * Helper function to get the total number of pages
 * @param state Current state
 * @returns Total number of pages
 */
export function getTotalPages(state: ConfigurationAssessmentState): number {
  const { pagination } = state;
  const { totalItems, pageSize } = pagination;
  
  return Math.ceil(totalItems / pageSize);
}

/**
 * Helper function to check if there is a next page
 * @param state Current state
 * @returns Whether there is a next page
 */
export function hasNextPage(state: ConfigurationAssessmentState): boolean {
  const { pagination } = state;
  const { currentPage } = pagination;
  
  return currentPage < getTotalPages(state) - 1;
}

/**
 * Helper function to check if there is a previous page
 * @param state Current state
 * @returns Whether there is a previous page
 */
export function hasPreviousPage(state: ConfigurationAssessmentState): boolean {
  const { pagination } = state;
  const { currentPage } = pagination;
  
  return currentPage > 0;
}

/**
 * Helper function to get the page range for pagination
 * @param state Current state
 * @param maxPages Maximum number of page buttons to show
 * @returns Array of page numbers to display
 */
export function getPageRange(
  state: ConfigurationAssessmentState,
  maxPages: number = 5
): number[] {
  const { pagination } = state;
  const { currentPage } = pagination;
  const totalPages = getTotalPages(state);
  
  if (totalPages <= maxPages) {
    return Array.from({ length: totalPages }, (_, i) => i);
  }
  
  const halfMax = Math.floor(maxPages / 2);
  let startPage = Math.max(0, currentPage - halfMax);
  let endPage = Math.min(totalPages - 1, startPage + maxPages - 1);
  
  if (endPage - startPage < maxPages - 1) {
    startPage = Math.max(0, endPage - maxPages + 1);
  }
  
  return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
}