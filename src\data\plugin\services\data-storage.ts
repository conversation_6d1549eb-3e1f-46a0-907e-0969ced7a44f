import { DataStorage } from '../interfaces';

/**
 * Implementation of the DataStorage interface
 */
export class DataStorageImpl implements DataStorage {
  private storage: Map<string, unknown> = new Map();
  private prefix = 'data_plugin_';

  /**
   * Gets a value from storage
   * @param key The storage key
   * @param defaultValue The default value if the key doesn't exist
   * @returns The stored value or the default value
   */
  public get<T>(key: string, defaultValue?: T): T | undefined {
    const fullKey = this.getFullKey(key);
    
    // First check in-memory cache
    if (this.storage.has(key)) {
      return this.storage.get(key);
    }
    
    // Then check localStorage
    try {
      const item = localStorage.getItem(fullKey);
      if (item !== null) {
        const value = JSON.parse(item) as T;
        // Cache in memory
        this.storage.set(key, value);
        return value;
      }
    } catch (e) {
      console.error(`Error retrieving ${key} from storage:`, e);
    }
    
    return defaultValue;
  }

  /**
   * Sets a value in storage
   * @param key The storage key
   * @param value The value to store
   */
  public set<T>(key: string, value: T): void {
    const fullKey = this.getFullKey(key);
    
    // Store in memory
    this.storage.set(key, value);
    
    // Store in localStorage
    try {
      localStorage.setItem(fullKey, JSON.stringify(value));
    } catch (e) {
      console.error(`Error storing ${key} in storage:`, e);
    }
  }

  /**
   * Removes a value from storage
   * @param key The storage key
   */
  public remove(key: string): void {
    const fullKey = this.getFullKey(key);
    
    // Remove from memory
    this.storage.delete(key);
    
    // Remove from localStorage
    try {
      localStorage.removeItem(fullKey);
    } catch (e) {
      console.error(`Error removing ${key} from storage:`, e);
    }
  }

  /**
   * Gets the full key with prefix
   * @param key The key
   * @returns The full key with prefix
   */
  private getFullKey(key: string): string {
    return `${this.prefix}${key}`;
  }
}