import { useMemo } from 'react';
import { useDiscover, discoverActions } from '../context/DiscoverContext';
import { DiscoverUtils } from '../utils/discoverUtils';
import { Filter } from '../types/discover';

/**
 * Hook for managing search and filtering functionality
 */
export const useDiscoverSearch = () => {
  const { state, dispatch } = useDiscover();
  const { searchQuery, appliedFilters, filteredData, logData } = state;
  
  // Get total count and filtered count
  const counts = useMemo(() => ({
    total: logData.length,
    filtered: filteredData.length,
  }), [logData.length, filteredData.length]);
  
  // Set search query
  const setSearchQuery = (query: string) => {
    dispatch(discoverActions.setSearchQuery(query));
  };
  
  // Parse search query into structured filters
  const parseQuery = (query: string) => {
    return DiscoverUtils.parseSearchQuery(query);
  };
  
  // Add filter
  const addFilter = (field: string, value: unknown, operator: 'is' | 'is not' | 'exists' | 'does not exist' | 'contains' = 'is') => {
    dispatch(discoverActions.addFilter(field, value, operator));
  };
  
  // Remove filter
  const removeFilter = (field: string) => {
    dispatch(discoverActions.removeFilter(field));
  };
  
  // Clear all filters
  const clearFilters = () => {
    appliedFilters.forEach(filter => {
      dispatch(discoverActions.removeFilter(filter.field));
    });
  };
  
  // Convert filters to query string
  const filtersToQueryString = (filters: Filter[], textSearch: string = '') => {
    return DiscoverUtils.filtersToQueryString(filters, textSearch);
  };
  
  // Get level distribution
  const levelDistribution = useMemo(() => {
    return DiscoverUtils.calculateLevelDistribution(filteredData);
  }, [filteredData]);
  
  // Get rule group distribution
  const ruleGroupDistribution = useMemo(() => {
    return DiscoverUtils.calculateRuleGroupDistribution(filteredData);
  }, [filteredData]);
  
  return {
    searchQuery,
    appliedFilters,
    filteredData,
    counts,
    setSearchQuery,
    parseQuery,
    addFilter,
    removeFilter,
    clearFilters,
    filtersToQueryString,
    levelDistribution,
    ruleGroupDistribution,
  };
};