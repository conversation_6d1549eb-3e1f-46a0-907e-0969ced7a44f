import React, { useEffect, useRef } from 'react';

/**
 * Component to manage focus and keyboard shortcuts for the Discover page
 */
const FocusManager: React.FC = () => {
  // Reference to track the last focused element
  const lastFocusedRef = useRef<HTMLElement | null>(null);
  
  // Handle focus management
  useEffect(() => {
    // Save the currently focused element when focus changes
    const handleFocusIn = (e: FocusEvent) => {
      if (e.target instanceof HTMLElement) {
        lastFocusedRef.current = e.target;
      }
    };
    
    // Handle keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard shortcuts when not in an input field
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA' || 
          document.activeElement?.tagName === 'SELECT') {
        return;
      }
      
      // Handle keyboard shortcuts
      switch (e.key) {
        case '/': {
          // Focus search input
          e.preventDefault();
          const searchInput = document.querySelector('input[placeholder="Search logs..."]') as HTMLInputElement;
          if (searchInput) {
            searchInput.focus();
          }
          break;
        }
          
        case 'f':
          // Focus search input (alternative)
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const searchInput = document.querySelector('input[placeholder="Search logs..."]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
            }
          }
          break;
          
        case 'r':
          // Refresh data
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const refreshButton = document.querySelector('button[aria-label="Refresh data"]') as HTMLButtonElement;
            if (refreshButton) {
              refreshButton.click();
            }
          }
          break;
          
        case 'h':
          // Toggle help
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const helpButton = document.querySelector('button[aria-label="Show help"]') as HTMLButtonElement;
            if (helpButton) {
              helpButton.click();
            }
          }
          break;
          
        case 'Escape':
          // Return focus to the last focused element
          if (lastFocusedRef.current) {
            lastFocusedRef.current.focus();
          }
          break;
      }
    };
    
    // Add event listeners
    document.addEventListener('focusin', handleFocusIn);
    document.addEventListener('keydown', handleKeyDown);
    
    // Remove event listeners on cleanup
    return () => {
      document.removeEventListener('focusin', handleFocusIn);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);
  
  // This component doesn't render anything
  return null;
};

export default FocusManager;