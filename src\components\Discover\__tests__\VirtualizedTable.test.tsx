import React from 'react';
import { render, screen } from '@testing-library/react';
import VirtualizedTable from '../VirtualizedTable';
import { LogEntry } from '../../../types/discover';

// Mock react-window and react-virtualized-auto-sizer
jest.mock('react-window', () => ({
  FixedSizeList: ({ children, itemCount }: { children: (props: { index: number; style: React.CSSProperties }) => React.ReactNode; itemCount: number }) => {
    const items = [];
    for (let i = 0; i < Math.min(itemCount, 10); i++) {
      items.push(children({ index: i, style: {} }));
    }
    return <div data-testid="virtual-list">{items}</div>;
  }
}));

jest.mock('react-virtualized-auto-sizer', () => ({ children }: { children: (props: { width: number; height: number }) => React.ReactNode }) => 
  children({ width: 1000, height: 500 })
);

describe('VirtualizedTable', () => {
  const mockData: LogEntry[] = [
    {
      id: '1',
      timestamp: '2023-01-01T12:00:00Z',
      message: 'Test message 1',
      level: 'info'
    },
    {
      id: '2',
      timestamp: '2023-01-02T12:00:00Z',
      message: 'Test message 2',
      level: 'error'
    }
  ];
  
  const mockSelectedFields = ['timestamp', 'message', 'level'];
  
  const mockFormatFieldValue = (value: unknown) => String(value);
  
  const mockGetFieldValue = (log: LogEntry, fieldPath: string) => {
    return log[fieldPath as keyof LogEntry];
  };
  
  it('should render the table header with selected fields', () => {
    render(
      <VirtualizedTable
        data={mockData}
        selectedFields={mockSelectedFields}
        formatFieldValue={mockFormatFieldValue}
        getFieldValue={mockGetFieldValue}
      />
    );
    
    // Check that all field headers are rendered
    mockSelectedFields.forEach(field => {
      expect(screen.getByText(field)).toBeInTheDocument();
    });
  });
  
  it('should render rows with expand buttons when onRowExpand is provided', () => {
    const mockOnRowExpand = jest.fn();
    const mockExpandedRows = { '1': true, '2': false };
    
    render(
      <VirtualizedTable
        data={mockData}
        selectedFields={mockSelectedFields}
        formatFieldValue={mockFormatFieldValue}
        getFieldValue={mockGetFieldValue}
        onRowExpand={mockOnRowExpand}
        expandedRows={mockExpandedRows}
      />
    );
    
    // Check that expand buttons are rendered
    const expandButtons = screen.getAllByRole('button');
    expect(expandButtons.length).toBe(2); // One for each row
    
    // Check that the first row has the expanded symbol and the second has the collapsed symbol
    expect(expandButtons[0].textContent).toBe('▼');
    expect(expandButtons[1].textContent).toBe('▶');
  });
  
  it('should not render expand buttons when onRowExpand is not provided', () => {
    render(
      <VirtualizedTable
        data={mockData}
        selectedFields={mockSelectedFields}
        formatFieldValue={mockFormatFieldValue}
        getFieldValue={mockGetFieldValue}
      />
    );
    
    // Check that no expand buttons are rendered
    const expandButtons = screen.queryAllByRole('button');
    expect(expandButtons.length).toBe(0);
  });
  
  it('should render field values correctly', () => {
    render(
      <VirtualizedTable
        data={mockData}
        selectedFields={mockSelectedFields}
        formatFieldValue={mockFormatFieldValue}
        getFieldValue={mockGetFieldValue}
      />
    );
    
    // Check that field values are rendered
    expect(screen.getByText('2023-01-01T12:00:00Z')).toBeInTheDocument();
    expect(screen.getByText('Test message 1')).toBeInTheDocument();
    expect(screen.getByText('info')).toBeInTheDocument();
    expect(screen.getByText('2023-01-02T12:00:00Z')).toBeInTheDocument();
    expect(screen.getByText('Test message 2')).toBeInTheDocument();
    expect(screen.getByText('error')).toBeInTheDocument();
  });
});