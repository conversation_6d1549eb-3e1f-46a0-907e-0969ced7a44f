import React, { useEffect, useState } from 'react';
import { DataPlugin } from '../data-plugin';
import { Query } from '../interfaces';

/**
 * Example component that demonstrates how to use the QueryService
 */
export const QueryServiceExample: React.FC = () => {
  const [query, setQuery] = useState<Query>({ query: '', language: 'kuery' });
  const [timeFrom, setTimeFrom] = useState<string>('now-15m');
  const [timeTo, setTimeTo] = useState<string>('now');
  const [filters, setFilters] = useState<Array<{ field: string; operator: string; value: unknown }>>([]);

  // Get the QueryService from the DataPlugin
  const queryService = DataPlugin.getInstance().getQueryService();

  // Subscribe to query updates
  useEffect(() => {
    const subscription = queryService.queryString.getUpdates$().subscribe((updatedQuery) => {
      setQuery(updatedQuery);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);

  // Subscribe to time filter updates
  useEffect(() => {
    const subscription = queryService.timefilter.getTimeUpdate$().subscribe((timeRange) => {
      setTimeFrom(timeRange.from);
      setTimeTo(timeRange.to);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);

  // Subscribe to filter updates
  useEffect(() => {
    const subscription = queryService.filterManager.getFiltersUpdate$().subscribe((updatedFilters) => {
      setFilters(updatedFilters);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);

  // Handle query input change
  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = { ...query, query: e.target.value };
    queryService.queryString.setQuery(newQuery);
  };

  // Handle language change
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newQuery = { ...query, language: e.target.value };
    queryService.queryString.setQuery(newQuery);
  };

  // Handle time range change
  const handleTimeFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    queryService.timefilter.setTime({ from: e.target.value, to: timeTo });
  };

  const handleTimeToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    queryService.timefilter.setTime({ from: timeFrom, to: e.target.value });
  };

  // Handle adding a filter
  const handleAddFilter = () => {
    queryService.filterManager.addFilter({
      field: 'example',
      operator: 'is',
      value: 'test'
    });
  };

  // Handle clearing filters
  const handleClearFilters = () => {
    queryService.filterManager.clearFilters();
  };

  return (
    <div>
      <h2>Query Service Example</h2>
      
      <div>
        <h3>Query</h3>
        <input
          type="text"
          value={query.query}
          onChange={handleQueryChange}
          placeholder="Enter query"
        />
        <select value={query.language} onChange={handleLanguageChange}>
          <option value="kuery">KQL</option>
          <option value="lucene">Lucene</option>
        </select>
      </div>
      
      <div>
        <h3>Time Range</h3>
        <div>
          <label>From: </label>
          <input
            type="text"
            value={timeFrom}
            onChange={handleTimeFromChange}
            placeholder="From time"
          />
        </div>
        <div>
          <label>To: </label>
          <input
            type="text"
            value={timeTo}
            onChange={handleTimeToChange}
            placeholder="To time"
          />
        </div>
      </div>
      
      <div>
        <h3>Filters</h3>
        <button onClick={handleAddFilter}>Add Example Filter</button>
        <button onClick={handleClearFilters}>Clear Filters</button>
        <ul>
          {filters.map((filter, index) => (
            <li key={index}>
              {filter.field} {filter.operator} {filter.value}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};