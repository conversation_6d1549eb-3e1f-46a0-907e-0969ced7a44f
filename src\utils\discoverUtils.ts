import { LogEntry, Filter, TimeRange, HistogramBucket, DiscoverState } from '../types/discover';
import { loadPageSizePreference } from './storageUtils';

export class DiscoverUtils {
  /**
   * Initialize default discover state
   */
    static getInitialState(): DiscoverState {
    const timeRange = this.getDefaultTimeRange();
    const logData: LogEntry[] = [];
    const defaultPageSize = 25; // Default page size as per requirements
    
    // Load page size preference from local storage or use default
    const savedPageSize = loadPageSizePreference(defaultPageSize);
    
    return {
      searchQuery: '',
      timeRange,
      selectedFields: ['timestamp', 'source', 'message', 'level', 'rule.description'],
      appliedFilters: [],
      logData,
      filteredData: logData,
      histogramData: [],
      isLoading: true,
      autoRefresh: false,
      refreshInterval: 60000, // 1 minute
      pagination: {
        currentPage: 1,
        pageSize: savedPageSize,
        totalItems: 0,
      },
    };
  }
  
  /**
   * Group fields by category for sidebar display
   */
  static categorizeFields(fields: Record<string, unknown>): Record<string, unknown> {
    const categories: Record<string, unknown> = {
      'Base fields': {},
      'Agent': {},
      'Rule': {},
      'Authentication': {},
      'Web': {},
      'Network': {},
      'System': {},
      'Other': {},
    };
    
    Object.entries(fields).forEach(([fieldName, fieldData]) => {
      if (fieldName.startsWith('agent.')) {
        categories['Agent'][fieldName] = fieldData;
      } else if (fieldName.startsWith('rule.')) {
        categories['Rule'][fieldName] = fieldData;
      } else if (fieldName.startsWith('auth.')) {
        categories['Authentication'][fieldName] = fieldData;
      } else if (fieldName.startsWith('http.')) {
        categories['Web'][fieldName] = fieldData;
      } else if (fieldName.startsWith('network.')) {
        categories['Network'][fieldName] = fieldData;
      } else if (fieldName.startsWith('system.')) {
        categories['System'][fieldName] = fieldData;
      } else if (['timestamp', 'source', 'message', 'level', 'location'].includes(fieldName)) {
        categories['Base fields'][fieldName] = fieldData;
      } else {
        categories['Other'][fieldName] = fieldData;
      }
    });
    
    // Remove empty categories
    Object.keys(categories).forEach(category => {
      if (Object.keys(categories[category]).length === 0) {
        delete categories[category];
      }
    });
    
    return categories;
  }
  /**
   * Filter log entries based on search query and applied filters
   */
  static filterLogData(
    logData: LogEntry[],
    searchQuery: string,
    filters: Filter[],
    timeRange: TimeRange
  ): LogEntry[] {
    let filteredData = [...logData];

    // Apply time range filter
    filteredData = filteredData.filter(log => 
      log.timestamp >= timeRange.from && log.timestamp <= timeRange.to
    );

    // Apply search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filteredData = filteredData.filter(log => 
        log.message.toLowerCase().indexOf(query) !== -1 ||
        log.source.toLowerCase().indexOf(query) !== -1 ||
        log.agent.name.toLowerCase().indexOf(query) !== -1 ||
        log.rule.description.toLowerCase().indexOf(query) !== -1 ||
        JSON.stringify(log.data).toLowerCase().indexOf(query) !== -1
      );
    }

    // Apply field filters
    filters.forEach(filter => {
      if (!filter.enabled) return;

      filteredData = filteredData.filter(log => {
        const fieldValue = this.getFieldValue(log, filter.field);
        
        switch (filter.operator) {
          case 'is':
            return fieldValue === filter.value;
          case 'is not':
            return fieldValue !== filter.value;
          case 'exists':
            return fieldValue !== undefined && fieldValue !== null;
          case 'does not exist':
            return fieldValue === undefined || fieldValue === null;
          case 'contains':
            return String(fieldValue).toLowerCase().indexOf(String(filter.value).toLowerCase()) !== -1;
          default:
            return true;
        }
      });
    });

    return filteredData;
  }

  /**
   * Generate histogram data from filtered log entries
   */
  static generateHistogramData(
    logData: LogEntry[],
    timeRange: TimeRange,
    bucketCount: number = 50
  ): HistogramBucket[] {
    const buckets: HistogramBucket[] = [];
    const timeSpan = timeRange.to.getTime() - timeRange.from.getTime();
    const bucketSize = timeSpan / bucketCount;

    // Initialize buckets
    for (let i = 0; i < bucketCount; i++) {
      const bucketStart = new Date(timeRange.from.getTime() + (i * bucketSize));
      buckets.push({
        timestamp: bucketStart,
        count: 0,
        interval: this.formatInterval(bucketSize),
      });
    }

    // Count logs in each bucket
    logData.forEach(log => {
      const logTime = log.timestamp.getTime();
      const bucketIndex = Math.floor((logTime - timeRange.from.getTime()) / bucketSize);
      
      if (bucketIndex >= 0 && bucketIndex < bucketCount) {
        buckets[bucketIndex].count++;
      }
    });

    return buckets;
  }

  /**
   * Get available fields from log data
   */
  static getAvailableFields(logData: LogEntry[]): Record<string, unknown> {
    const fieldStats: Record<string, unknown> = {};

    // Standard fields
    const standardFields = ['timestamp', 'source', 'message', 'level', 'location'];
    standardFields.forEach(field => {
      fieldStats[field] = {
        type: 'string',
        count: logData.length,
        values: this.getTopValues(logData, field),
      };
    });

    // Agent fields
    fieldStats['agent.id'] = {
      type: 'string',
      count: logData.length,
      values: this.getTopValues(logData, 'agent.id'),
    };
    fieldStats['agent.name'] = {
      type: 'string',
      count: logData.length,
      values: this.getTopValues(logData, 'agent.name'),
    };
    fieldStats['agent.ip'] = {
      type: 'string',
      count: logData.length,
      values: this.getTopValues(logData, 'agent.ip'),
    };

    // Rule fields
    fieldStats['rule.id'] = {
      type: 'number',
      count: logData.length,
      values: this.getTopValues(logData, 'rule.id'),
    };
    fieldStats['rule.description'] = {
      type: 'string',
      count: logData.length,
      values: this.getTopValues(logData, 'rule.description'),
    };
    fieldStats['rule.level'] = {
      type: 'number',
      count: logData.length,
      values: this.getTopValues(logData, 'rule.level'),
    };

    // Dynamic fields from data object
    const dynamicFields: string[] = [];
    logData.forEach(log => {
      Object.keys(log.data).forEach(key => {
        if (dynamicFields.indexOf(key) === -1) {
          dynamicFields.push(key);
        }
      });
    });

    dynamicFields.forEach(field => {
      fieldStats[`data.${field}`] = {
        type: 'mixed',
        count: logData.filter(log => log.data[field] !== undefined).length,
        values: this.getTopValues(logData, `data.${field}`),
      };
    });

    return fieldStats;
  }

  /**
   * Get field value from log entry using dot notation
   */
  private static getFieldValue(log: LogEntry, fieldPath: string): unknown {
    const parts = fieldPath.split('.');
    let value: unknown = log;

    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Get top values for a specific field
   */
  private static getTopValues(logData: LogEntry[], fieldPath: string, limit: number = 10): Array<{ value: unknown; count: number }> {
    const valueCounts: Record<string, number> = {};

    logData.forEach(log => {
      const value = this.getFieldValue(log, fieldPath);
      if (value !== undefined && value !== null) {
        const key = String(value);
        valueCounts[key] = (valueCounts[key] || 0) + 1;
      }
    });

    const results: Array<{ value: unknown; count: number }> = [];
    for (const key in valueCounts) {
      if (Object.prototype.hasOwnProperty.call(valueCounts, key)) {
        results.push({ value: key, count: valueCounts[key] });
      }
    }
    
    return results
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Format time interval for display
   */
  private static formatInterval(milliseconds: number): string {
    const seconds = milliseconds / 1000;
    const minutes = seconds / 60;
    const hours = minutes / 60;
    const days = hours / 24;

    if (days >= 1) return `${Math.round(days)}d`;
    if (hours >= 1) return `${Math.round(hours)}h`;
    if (minutes >= 1) return `${Math.round(minutes)}m`;
    return `${Math.round(seconds)}s`;
  }

  /**
   * Create default time range (last 24 hours)
   */
  static getDefaultTimeRange(): TimeRange {
    const now = new Date();
    const yesterday = new Date(now.getTime() - (24 * 60 * 60 * 1000));
    
    return {
      from: yesterday,
      to: now,
      preset: 'last-24h',
    };
  }

  /**
   * Create time range from preset
   */
  static createTimeRangeFromPreset(preset: string): TimeRange {
    const now = new Date();
    let from: Date;

    switch (preset) {
      case 'last-15m':
        from = new Date(now.getTime() - (15 * 60 * 1000));
        break;
      case 'last-1h':
        from = new Date(now.getTime() - (60 * 60 * 1000));
        break;
      case 'last-24h':
        from = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        break;
      case 'last-7d':
        from = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        break;
      case 'last-30d':
        from = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        break;
      default:
        from = new Date(now.getTime() - (24 * 60 * 60 * 1000));
    }

    return {
      from,
      to: now,
      preset: preset as TimeRange['preset'],
    };
  }

  /**
   * Format date for display
   */
  static formatDate(date: Date, includeTime: boolean = true): string {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };

    if (includeTime) {
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.second = '2-digit';
    }

    return new Intl.DateTimeFormat('en-US', options).format(date);
  }

  /**
   * Create a filter from field and value
   */
  static createFilter(field: string, value: unknown, operator: 'is' | 'is not' | 'exists' | 'does not exist' | 'contains' = 'is'): Filter {
    return {
      field,
      operator,
      value,
      enabled: true,
    };
  }

  /**
   * Get field type from sample value
   */
  static getFieldType(value: unknown): string {
    if (value === null || value === undefined) return 'unknown';
    if (value instanceof Date) return 'date';
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    return typeof value;
  }

  /**
   * Format field value for display
   */
  static formatFieldValue(value: unknown): string {
    if (value === null || value === undefined) return '-';
    if (value instanceof Date) return this.formatDate(value);
    if (Array.isArray(value)) return value.join(', ');
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  }

  /**
   * Calculate distribution of log levels
   */
  static calculateLevelDistribution(logData: LogEntry[]): Record<string, number> {
    const distribution: Record<string, number> = {
      info: 0,
      warning: 0,
      error: 0,
      critical: 0,
    };

    logData.forEach(log => {
      distribution[log.level]++;
    });

    return distribution;
  }

  /**
   * Calculate distribution of rule groups
   */
  static calculateRuleGroupDistribution(logData: LogEntry[]): Record<string, number> {
    const distribution: Record<string, number> = {};

    logData.forEach(log => {
      if (log.rule && log.rule.groups) {
        log.rule.groups.forEach(group => {
          distribution[group] = (distribution[group] || 0) + 1;
        });
      }
    });

    return distribution;
  }

  /**
   * Extract unique field paths from log data
   * This is useful for discovering all available fields in the dataset
   */
  static extractFieldPaths(logData: LogEntry[]): string[] {
    const paths = new Set<string>();
    
    // Add standard fields
    ['id', 'timestamp', 'source', 'message', 'level', 'location'].forEach(field => paths.add(field));
    
    // Add agent fields
    ['agent.id', 'agent.name', 'agent.ip'].forEach(field => paths.add(field));
    
    // Add rule fields
    ['rule.id', 'rule.description', 'rule.level', 'rule.groups'].forEach(field => paths.add(field));
    
    // Add decoder fields
    ['decoder.name'].forEach(field => paths.add(field));
    
    // Extract dynamic fields from data object
    logData.forEach(log => {
      this.extractNestedPaths(log.data, 'data').forEach(path => paths.add(path));
    });
    
    return Array.from(paths).sort();
  }
  
  /**
   * Extract nested field paths from an object
   */
  private static extractNestedPaths(obj: unknown, prefix: string): string[] {
    if (!obj || typeof obj !== 'object') return [];
    
    const paths: string[] = [];
    
    Object.entries(obj).forEach(([key, value]) => {
      const path = `${prefix}.${key}`;
      paths.push(path);
      
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        this.extractNestedPaths(value, path).forEach(nestedPath => paths.push(nestedPath));
      }
    });
    
    return paths;
  }

  /**
   * Group logs by time interval for histogram visualization
   */
  static groupLogsByTimeInterval(
    logs: LogEntry[],
    interval: 'minute' | 'hour' | 'day' | 'week' = 'hour'
  ): { timestamp: Date; count: number }[] {
    const result: Record<string, number> = {};
    
    logs.forEach(log => {
      const date = new Date(log.timestamp);
      let key: string;
      
      switch (interval) {
        case 'minute': {
          date.setSeconds(0, 0);
          key = date.toISOString();
          break;
        }
        case 'hour': {
          date.setMinutes(0, 0, 0);
          key = date.toISOString();
          break;
        }
        case 'day': {
          date.setHours(0, 0, 0, 0);
          key = date.toISOString();
          break;
        }
        case 'week': {
          const dayOfWeek = date.getDay();
          const diff = date.getDate() - dayOfWeek;
          date.setDate(diff);
          date.setHours(0, 0, 0, 0);
          key = date.toISOString();
          break;
        }
      }
      
      result[key] = (result[key] || 0) + 1;
    });
    
    return Object.entries(result)
      .map(([key, count]) => ({ timestamp: new Date(key), count }))
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Get appropriate time interval based on time range
   */
  static getAppropriateTimeInterval(timeRange: TimeRange): 'minute' | 'hour' | 'day' | 'week' {
    const diffMs = timeRange.to.getTime() - timeRange.from.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    
    if (diffHours <= 6) return 'minute';
    if (diffHours <= 48) return 'hour';
    if (diffHours <= 168) return 'day'; // 7 days
    return 'week';
  }

  /**
   * Parse search query into structured filters
   * Supports basic query syntax like field:value
   */
  static parseSearchQuery(query: string): { textSearch: string; filters: Filter[] } {
    const filters: Filter[] = [];
    let textSearch = '';
    
    // Split by spaces, but respect quoted strings
    const parts: string[] = [];
    let currentPart = '';
    let inQuotes = false;
    
    for (let i = 0; i < query.length; i++) {
      const char = query[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
        currentPart += char;
      } else if (char === ' ' && !inQuotes) {
        if (currentPart) {
          parts.push(currentPart);
          currentPart = '';
        }
      } else {
        currentPart += char;
      }
    }
    
    if (currentPart) {
      parts.push(currentPart);
    }
    
    // Process each part
    const textParts: string[] = [];
    
    parts.forEach(part => {
      // Check if it's a field:value filter
      const colonIndex = part.indexOf(':');
      let field: string;
      let value: string | unknown;

      if (colonIndex > 0) {
        field = part.substring(0, colonIndex).trim();
        value = part.substring(colonIndex + 1).trim();
        
        // Remove quotes if present
        if (typeof value === 'string' && value.startsWith('"') && value.endsWith('"')) {
          value = value.substring(1, value.length - 1);
        }
        
        // Handle negation
        if (field.startsWith('-')) {
          filters.push({
            field: field.substring(1),
            operator: 'is not',
            value,
            enabled: true,
          });
        } else {
          filters.push({
            field,
            operator: 'is',
            value,
            enabled: true,
          });
        }
      } else {
        textParts.push(part);
      }
    });
    
    textSearch = textParts.join(' ');
    
    return { textSearch, filters };
  }

  /**
   * Convert filters to query string
   */
  static filtersToQueryString(filters: Filter[], textSearch: string = ''): string {
    const filterStrings = filters.map(filter => {
      if (!filter.enabled) return '';
      
      const value = typeof filter.value === 'string' && filter.value.includes(' ') 
        ? `"${filter.value}"` 
        : filter.value;
      
      switch (filter.operator) {
        case 'is':
          return `${filter.field}:${value}`;
        case 'is not':
          return `-${filter.field}:${value}`;
        case 'exists':
          return `_exists_:${filter.field}`;
        case 'does not exist':
          return `-_exists_:${filter.field}`;
        case 'contains':
          return `${filter.field}:*${value}*`;
        default:
          return '';
      }
    }).filter(Boolean);
    
    if (textSearch) {
      filterStrings.push(textSearch);
    }
    
    return filterStrings.join(' ');
  }

  /**
   * Get color for log level
   */
  static getLevelColor(level: 'info' | 'warning' | 'error' | 'critical'): string {
    switch (level) {
      case 'info':
        return '#00bfff'; // Light blue
      case 'warning':
        return '#ffa500'; // Orange
      case 'error':
        return '#ff4500'; // Red-orange
      case 'critical':
        return '#ff0000'; // Red
      default:
        return '#00bfff'; // Default to light blue
    }
  }

  /**
   * Format number with thousands separators
   */
  static formatNumber(num: number): string {
    return new Intl.NumberFormat().format(num);
  }
}
