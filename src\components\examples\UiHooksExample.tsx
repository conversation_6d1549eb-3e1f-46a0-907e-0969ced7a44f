import React, { useState } from 'react';
import { 
  useUiComponent, 
  useFieldFormat, 
  useAutocomplete, 
  useLanguageAutocomplete,
  useUiComponentWithState
} from '../../hooks/uiHooks';

/**
 * Example component that demonstrates how to use the UI hooks
 */
const UiHooksExample: React.FC = () => {
  // Example of using useUiComponent
  const DataTable = useUiComponent('dataTable');
  
  // Example of using useFieldFormat
  const { formatValue: formatDate } = useFieldFormat('date', 'date');
  const { formatValue: formatNumber } = useFieldFormat('number', 'number');
  
  // Example of using useAutocomplete
  const { getCompletions, hasProvider } = useAutocomplete('field');
  const [suggestions, setSuggestions] = useState<Array<{ value: string; score: number }>>([]);
  const [inputValue, setInputValue] = useState('');
  
  // Example of using useLanguageAutocomplete
  const { 
    validateQuery, 
    highlightQuery, 
    /* getCompletions: getLanguageCompletions */ 
  } = useLanguageAutocomplete('kuery');
  const [queryValue, setQueryValue] = useState('');
  const [isValid, setIsValid] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const [highlightedQuery, setHighlightedQuery] = useState('');
  
  // Example of using useUiComponentWithState
  const { 
    component: FilterBar, 
    props: filterBarProps, 
    updateProps: updateFilterBarProps 
  } = useUiComponentWithState('filterBar', { filters: [] });
  
  // Handle input change for autocomplete
  const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    if (hasProvider && value.length > 0) {
      const completions = await getCompletions({ query: value });
      setSuggestions(completions);
    } else {
      setSuggestions([]);
    }
  };
  
  // Handle query change for language autocomplete
  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQueryValue(value);
    
    // Validate the query
    const validation = validateQuery(value);
    setIsValid(validation.valid);
    setErrorMessage(validation.error || '');
    
    // Highlight the query
    setHighlightedQuery(highlightQuery(value));
  };
  
  // Add a filter
  const handleAddFilter = () => {
    updateFilterBarProps({
      filters: [
        ...filterBarProps.filters,
        { field: 'example', value: 'test', operator: 'is' }
      ]
    });
  };
  
  return (
    <div className="ui-hooks-example">
      <h2>UI Hooks Example</h2>
      
      <div className="section">
        <h3>Field Formatting</h3>
        <div className="field-formatting">
          <div className="example">
            <h4>Date Formatting</h4>
            <p>Original: {new Date().toISOString()}</p>
            <p>Formatted: {formatDate(new Date())}</p>
          </div>
          
          <div className="example">
            <h4>Number Formatting</h4>
            <p>Original: {1234.5678}</p>
            <p>Formatted: {formatNumber(1234.5678)}</p>
          </div>
        </div>
      </div>
      
      <div className="section">
        <h3>Autocomplete</h3>
        <div className="autocomplete">
          <div className="form-group">
            <label>Field:</label>
            <input 
              type="text" 
              value={inputValue} 
              onChange={handleInputChange} 
              placeholder="Type to see suggestions"
            />
          </div>
          
          {suggestions.length > 0 && (
            <ul className="suggestions">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="suggestion-item">
                  {suggestion.value} (score: {suggestion.score})
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>Language Autocomplete</h3>
        <div className="language-autocomplete">
          <div className="form-group">
            <label>Query (KQL):</label>
            <input 
              type="text" 
              value={queryValue} 
              onChange={handleQueryChange} 
              placeholder="Enter a KQL query"
              className={isValid ? 'valid' : 'invalid'}
            />
            {!isValid && errorMessage && (
              <div className="error-message">{errorMessage}</div>
            )}
          </div>
          
          {highlightedQuery && (
            <div className="highlighted-query">
              <h4>Highlighted Query</h4>
              <div dangerouslySetInnerHTML={{ __html: highlightedQuery }} />
            </div>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>UI Component with State</h3>
        <div className="ui-component-state">
          <button onClick={handleAddFilter}>Add Filter</button>
          
          <div className="filter-count">
            Current filters: {filterBarProps.filters.length}
          </div>
          
          {FilterBar && (
            <div className="filter-bar-container">
              <FilterBar {...filterBarProps} />
            </div>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>Dynamic UI Component</h3>
        <div className="dynamic-component">
          {DataTable ? (
            <DataTable 
              data={[
                { id: 1, name: 'Item 1', value: 100 },
                { id: 2, name: 'Item 2', value: 200 },
                { id: 3, name: 'Item 3', value: 300 }
              ]}
              columns={[
                { field: 'id', name: 'ID' },
                { field: 'name', name: 'Name' },
                { field: 'value', name: 'Value' }
              ]}
            />
          ) : (
            <p>DataTable component not registered</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default UiHooksExample;