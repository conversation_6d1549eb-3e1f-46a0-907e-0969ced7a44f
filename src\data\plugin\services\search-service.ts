import { SearchService, SearchSource, SearchResponse, SearchRequest, SearchOptions } from '../interfaces';
import { SearchInterceptor } from '../search/search-interceptor';
import { lastValueFrom } from 'rxjs';
import { IndexPatternService } from './index-pattern-service';
import { QueryCacheService } from './query-cache-service';

/**
 * Implementation of the SearchSource interface
 * SearchSource is responsible for configuring and executing searches
 */
class SearchSourceImpl implements SearchSource {
  private fields: Record<string, unknown> = {};
  private interceptorChain: SearchInterceptor | null = null;
  private queryCache: QueryCacheService | null = null;

  /**
   * Constructor for SearchSourceImpl
   * @param interceptorChain The chain of search interceptors to use for search execution
   * @param queryCache Optional query cache service
   */
  constructor(
    interceptorChain: SearchInterceptor | null = null,
    queryCache: QueryCacheService | null = null
  ) {
    this.interceptorChain = interceptorChain;
    this.queryCache = queryCache;
  }

  // Cache for memoizing request creation
  private lastRequest: SearchRequest | null = null;
  private lastFields: Record<string, unknown> | null = null;

  /**
   * Sets a field in the search source
   * @param field The field name
   * @param value The field value
   * @returns This SearchSource for chaining
   */
  public setField(field: string, value: unknown): SearchSource {
    this.fields[field] = value;
    return this;
  }

  /**
   * Gets a field value from the search source
   * @param field The field name
   * @returns The field value
   */
  public getField(field: string): unknown {
    return this.fields[field];
  }

  /**
   * Executes the search with the configured fields
   * @param options Optional search options
   * @returns A promise that resolves to the search response
   */
  public async fetch(options: SearchOptions = {}): Promise<SearchResponse> {
    try {
      // Create a search request from the fields
      const request: SearchRequest = this.createSearchRequest();

      // Check cache first if available and not explicitly bypassed
      if (this.queryCache && !options.bypassCache) {
        const cachedResponse = this.queryCache.get(request);
        if (cachedResponse) {
          console.log('Using cached search response');
          return cachedResponse;
        }
      }

      // If we have an interceptor chain, use it to execute the search
      if (this.interceptorChain) {
        const response$ = this.interceptorChain.search(request, options);
        const response = await lastValueFrom(response$);

        // Cache the response if caching is enabled
        if (this.queryCache && !options.bypassCache) {
          this.queryCache.set(request, response);
        }

        return response;
      }

      // Fallback implementation if no interceptor chain is available
      const response = await this.fallbackSearch(request);

      // Cache the response if caching is enabled
      if (this.queryCache && !options.bypassCache) {
        this.queryCache.set(request, response);
      }

      return response;
    } catch (error) {
      console.error('Error executing search:', error);
      throw error;
    }
  }

  /**
   * Creates a search request from the configured fields
   * @returns The search request
   */
  private createSearchRequest(): SearchRequest {
    // Check if fields have changed since last request
    if (this.lastRequest && this.lastFields &&
      JSON.stringify(this.fields) === JSON.stringify(this.lastFields)) {
      return this.lastRequest;
    }

    const query = this.fields.query || { query: '', language: 'kuery' };
    const index = this.fields.index;
    const size = this.fields.size || 10;
    const from = this.fields.from || 0;
    const sort = this.fields.sort;
    const fields = this.fields.fields;
    const highlight = this.fields.highlight;

    const request = {
      query,
      index,
      size,
      from,
      sort,
      fields,
      highlight
    };

    // Cache the request and fields for future comparison
    this.lastRequest = request;
    this.lastFields = { ...this.fields };

    return request;
  }

  /**
   * Fallback search implementation when no interceptor chain is available
   * @param request The search request
   * @returns A promise that resolves to the search response
   */
  private async fallbackSearch(_request: SearchRequest): Promise<SearchResponse> {
    console.warn('Using fallback search implementation. No interceptor chain available.');

    // This is a placeholder implementation
    // In a real implementation, this would use a default strategy to execute the search
    return {
      hits: {
        total: 0,
        hits: []
      },
      took: 0,
      timed_out: false
    };
  }
}

/**
 * Implementation of the SearchService interface
 * SearchService is responsible for creating SearchSource instances and managing search interceptors
 */
export class SearchServiceImpl implements SearchService {
  private interceptorChain: SearchInterceptor | null = null;
  private indexPatternService: IndexPatternService;
  private queryCache: QueryCacheService;

  constructor(indexPatternService?: IndexPatternService) {
    // Create or use the provided index pattern service
    this.indexPatternService = indexPatternService || new IndexPatternService();

    // Create the query cache service
    this.queryCache = new QueryCacheService();

    // Initialize the interceptor chain asynchronously
    // We can't await in constructor, so we'll initialize it but the chain might not be ready immediately
    this.setupInterceptorChain().catch(error => {
      console.error('Failed to set up interceptor chain:', error);
    });
  }

  /**
   * The searchSource factory
   */
  public searchSource = {
    /**
     * Creates a new SearchSource
     * @returns A promise that resolves to a new SearchSource
     */
    create: async (): Promise<SearchSource> => {
      return new SearchSourceImpl(this.interceptorChain, this.queryCache);
    }
  };

  /**
   * Gets the index pattern service
   * @returns The index pattern service
   */
  public getIndexPatternService(): IndexPatternService {
    return this.indexPatternService;
  }

  /**
   * Sets up the interceptor chain
   * This method can be extended to add more interceptors
   */
  private async setupInterceptorChain(): Promise<void> {
    try {
      // Import the interceptors dynamically using ES module imports to avoid circular dependencies
      const [
        { SampleSearchInterceptor },
        { BaseSearchInterceptor },
        { IndexPatternInterceptor },
        { CustomSampleInterceptor }
      ] = await Promise.all([
        import('../search/sample-search-interceptor'),
        import('../search/base-search-interceptor'),
        import('../search/index-pattern-interceptor'),
        import('../search/custom-sample-interceptor')
      ]);

      // Create the base interceptor
      const baseInterceptor = new BaseSearchInterceptor();

      // Create the sample interceptor
      const sampleInterceptor = new SampleSearchInterceptor();

      // Create the custom sample interceptor
      const customSampleInterceptor = new CustomSampleInterceptor();

      // Create the index pattern interceptor
      const indexPatternInterceptor = new IndexPatternInterceptor(this.indexPatternService);

      // Set up the chain
      this.interceptorChain = indexPatternInterceptor;
      indexPatternInterceptor.setNext(customSampleInterceptor);
      customSampleInterceptor.setNext(sampleInterceptor);
      sampleInterceptor.setNext(baseInterceptor);
    } catch (error) {
      console.error('Error setting up interceptor chain:', error);
      throw error;
    }
  }
}