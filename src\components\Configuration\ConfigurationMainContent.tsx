import React, { useState } from 'react';
import { useConfigurationAssessment } from '../../context/ConfigurationAssessmentContext';
import ConfigurationToolbar from './ConfigurationToolbar';
import { ConfigurationTable } from '../ConfigurationAssessment';
import ConfigurationHistogram from './ConfigurationHistogram';
import ConfigurationStats from './ConfigurationStats';
import ConfigurationLoadingState from './ConfigurationLoadingState';
import ConfigurationErrorState from './ConfigurationErrorState';
import ConfigurationEmptyState from './ConfigurationEmptyState';

/**
 * Main content component for the Configuration Assessment page
 * Contains the toolbar, histogram, and configuration table
 * 
 * Requirements: 1.1, 1.2, 2.1, 2.2, 6.1
 */
const ConfigurationMainContent: React.FC = () => {
  const { state } = useConfigurationAssessment();
  const { isLoading, filteredData } = state;
  const [activeTab, setActiveTab] = useState<'configurations' | 'statistics' | 'distribution'>('configurations');
  const hasEntries = filteredData.length > 0;
  
  return (
    <div style={{
      flexGrow: 1,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
    }}>
      {/* Toolbar with search and controls */}
      <ConfigurationToolbar />
      
      {/* Loading and error states */}
      {isLoading && <ConfigurationLoadingState />}
      <ConfigurationErrorState />
      
      {/* Main content area with tabs */}
      <div style={{
        flexGrow: 1,
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}>
        {/* Tab bar */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
          marginBottom: '16px',
        }}>
          <button
            onClick={() => setActiveTab('configurations')}
            style={{
              background: 'transparent',
              border: 'none',
              borderBottom: activeTab === 'configurations' ? '2px solid #00e5ff' : '2px solid transparent',
              color: activeTab === 'configurations' ? 'white' : 'rgba(255,255,255,0.6)',
              padding: '12px 24px',
              fontSize: '15px',
              cursor: 'pointer',
              fontWeight: activeTab === 'configurations' ? 'bold' : 'normal',
              outline: 'none',
              transition: 'color 0.2s',
            }}
          >
            Configurations
          </button>
          <button
            onClick={() => hasEntries && setActiveTab('statistics')}
            disabled={!hasEntries}
            style={{
              background: 'transparent',
              border: 'none',
              borderBottom: activeTab === 'statistics' ? '2px solid #00e5ff' : '2px solid transparent',
              color: !hasEntries ? 'rgba(255,255,255,0.3)' : (activeTab === 'statistics' ? 'white' : 'rgba(255,255,255,0.6)'),
              padding: '12px 24px',
              fontSize: '15px',
              cursor: hasEntries ? 'pointer' : 'not-allowed',
              fontWeight: activeTab === 'statistics' ? 'bold' : 'normal',
              outline: 'none',
              transition: 'color 0.2s',
            }}
          >
            Statistics
          </button>
          <button
            onClick={() => hasEntries && setActiveTab('distribution')}
            disabled={!hasEntries}
            style={{
              background: 'transparent',
              border: 'none',
              borderBottom: activeTab === 'distribution' ? '2px solid #00e5ff' : '2px solid transparent',
              color: !hasEntries ? 'rgba(255,255,255,0.3)' : (activeTab === 'distribution' ? 'white' : 'rgba(255,255,255,0.6)'),
              padding: '12px 24px',
              fontSize: '15px',
              cursor: hasEntries ? 'pointer' : 'not-allowed',
              fontWeight: activeTab === 'distribution' ? 'bold' : 'normal',
              outline: 'none',
              transition: 'color 0.2s',
            }}
          >
            Distribution
          </button>
        </div>
        
        {/* Tab content */}
        <div style={{ flexGrow: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
          {activeTab === 'configurations' && (
            <div style={{ flexGrow: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
              <div style={{
                flexGrow: 1,
                minHeight: 0,
                background: 'rgba(16, 24, 45, 0.7)',
                borderRadius: '8px',
                overflow: 'hidden',
                border: '1px solid rgba(0, 229, 255, 0.2)',
                position: 'relative',
              }}>
                {filteredData.length === 0 && !isLoading ? (
                  <ConfigurationEmptyState />
                ) : (
                  <ConfigurationTable />
                )}
              </div>
            </div>
          )}
          
          {activeTab === 'statistics' && (
            <div style={{
              flexGrow: 1,
              background: 'rgba(16, 24, 45, 0.7)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'stretch',
              justifyContent: 'flex-start',
            }}>
              {hasEntries ? <ConfigurationStats /> : <ConfigurationEmptyState message="No statistics available" subMessage="There are no configuration entries to display statistics for." />}
            </div>
          )}
          
          {activeTab === 'distribution' && (
            <div style={{
              flexGrow: 1,
              background: 'rgba(16, 24, 45, 0.7)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'stretch',
              justifyContent: 'flex-start',
            }}>
              {hasEntries ? <ConfigurationHistogram /> : <ConfigurationEmptyState message="No distribution data available" subMessage="There are no configuration entries to display distribution for." />}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConfigurationMainContent;