import React, { useState, useCallback, useMemo } from 'react';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';
import { useSearch } from '../../hooks/dataHooks';
import { useFieldFormat } from '../../hooks/uiHooks';
import { LogEntry } from '../../types/discover';

import VirtualizedTable from './VirtualizedTable';

interface DiscoverTableProps {
  className?: string;
  pageSize?: number;
  showPagination?: boolean;
  showExpandedView?: boolean;
}

/**
 * DiscoverTable component that displays log data with pagination and expandable rows
 */
const DiscoverTable: React.FC<DiscoverTableProps> = ({
  className = '',
  showPagination = true,
  showExpandedView = true
}) => {
  const { state, dispatch } = useDiscover();
  const { /* results, */ isLoading, error } = useSearch();
  
  // State for expanded rows
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  
  // Format functions
  const { formatValue: formatDate } = useFieldFormat('date');
  
  // Calculate pagination
  const totalPages = Math.ceil(state.filteredData.length / state.pagination.pageSize);
  const startIndex = (state.pagination.currentPage - 1) * state.pagination.pageSize;
  const endIndex = Math.min(startIndex + state.pagination.pageSize, state.filteredData.length);
  const currentPageData = state.filteredData.slice(startIndex, endIndex);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    dispatch(discoverActions.setCurrentPage(page));
  };
  
  // Handle page size change
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = parseInt(e.target.value, 10);
    dispatch(discoverActions.setPageSize(newPageSize));
  };
  
  // Toggle row expansion
  const toggleRowExpansion = (id: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };
  
  
  
  // Get field value from log entry
  const getFieldValue = (log: LogEntry, fieldPath: string): unknown => {
    const parts = fieldPath.split('.');
    let value: unknown = log;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = (value as Record<string, unknown>)[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  };
  
  // Format field value for display
  const formatFieldValue = useCallback((value: unknown, fieldName: string): string => {
    if (value === undefined || value === null) {
      return '-';
    }
    
    if (fieldName === 'timestamp' || fieldName.endsWith('.timestamp')) {
      return formatDate(value);
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }, [formatDate]);
  
  // Render table header
  const renderTableHeader = () => {
    return (
      <thead>
        <tr>
          {showExpandedView && <th className="expand-column"></th>}
          {state.selectedFields.map(field => (
            <th key={field} className="field-column">
              {field}
            </th>
          ))}
        </tr>
      </thead>
    );
  };
  
  // Expanded row details component
  const ExpandedRowDetails = useCallback(({ log }: { log: LogEntry }) => {
    return (
      <div className="expanded-content">
        <h4>Log Details</h4>
        <div className="log-details">
          {Object.entries(log).map(([key, value]) => {
            if (key === 'id') return null;
            
            if (typeof value === 'object' && value !== null) {
              return (
                <div key={key} className="log-detail-group">
                  <h5>{key}</h5>
                  <div className="log-detail-group-content">
                    {Object.entries(value).map(([subKey, subValue]) => (
                      <div key={`${key}-${subKey}`} className="log-detail-item">
                        <span className="log-detail-key">{subKey}:</span>
                        <span className="log-detail-value">
                          {formatFieldValue(subValue, `${key}.${subKey}`)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            }
            
            return (
              <div key={key} className="log-detail-item">
                <span className="log-detail-key">{key}:</span>
                <span className="log-detail-value">
                  {formatFieldValue(value, key)}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  }, [formatFieldValue]);
  
  // Memoized expanded rows for better performance
  const expandedRowsData = useMemo(() => {
    return Object.entries(expandedRows)
      .filter(([, isExpanded]) => isExpanded)
      .map(([id]) => {
        const log = state.filteredData.find(log => log.id === id);
        return log ? { id, log } : null;
      })
      .filter(Boolean);
  }, [expandedRows, state.filteredData]);
  
  // Render pagination controls
  const renderPagination = () => {
    if (!showPagination) return null;
    
    return (
      <div className="table-pagination">
        <div className="pagination-info">
          Showing {startIndex + 1} to {endIndex} of {state.filteredData.length} entries
        </div>
        
        <div className="pagination-controls">
          <button
            onClick={() => handlePageChange(1)}
            disabled={state.pagination.currentPage === 1}
            className="pagination-button"
          >
            First
          </button>
          <button
            onClick={() => handlePageChange(state.pagination.currentPage - 1)}
            disabled={state.pagination.currentPage === 1}
            className="pagination-button"
          >
            Previous
          </button>
          
          <span className="pagination-pages">
            Page {state.pagination.currentPage} of {totalPages}
          </span>
          
          <button
            onClick={() => handlePageChange(state.pagination.currentPage + 1)}
            disabled={state.pagination.currentPage === totalPages}
            className="pagination-button"
          >
            Next
          </button>
          <button
            onClick={() => handlePageChange(totalPages)}
            disabled={state.pagination.currentPage === totalPages}
            className="pagination-button"
          >
            Last
          </button>
        </div>
        
        <div className="pagination-size">
          <label>
            Show
            <select
              value={state.pagination.pageSize}
              onChange={handlePageSizeChange}
              className="page-size-select"
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            entries
          </label>
        </div>
      </div>
    );
  };
  
  // Use virtualized table for better performance with large datasets
  const useVirtualization = useMemo(() => state.filteredData.length > 100, [state.filteredData.length]);
  
  return (
    <div className={`discover-table ${className}`} style={{ height: '600px' }}>
      {isLoading && (
        <div className="table-loading">
          <p>Loading...</p>
        </div>
      )}
      
      {error && (
        <div className="table-error">
          <p>Error: {error.message}</p>
        </div>
      )}
      
      {!isLoading && !error && state.filteredData.length === 0 && (
        <div className="table-empty">
          <p>No data found</p>
        </div>
      )}
      
      {!isLoading && !error && state.filteredData.length > 0 && (
        <>
          {useVirtualization ? (
            <div className="virtualized-table-container" style={{ height: '500px' }}>
              <VirtualizedTable
                data={currentPageData}
                selectedFields={state.selectedFields}
                formatFieldValue={formatFieldValue}
                getFieldValue={getFieldValue}
                onRowExpand={showExpandedView ? toggleRowExpansion : undefined}
                expandedRows={expandedRows}
                className="virtualized-log-table"
              />
              
              {/* Render expanded rows separately for better performance */}
              <div className="expanded-rows-container">
                {expandedRowsData.map(item => item && (
                  <div key={item.id} className="expanded-row-details">
                    <ExpandedRowDetails log={item.log} />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="table-container">
              <table className="log-table">
                {renderTableHeader()}
                <tbody>
                  {currentPageData.map(log => (
                    <React.Fragment key={log.id}>
                      <tr className={`log-row ${expandedRows[log.id] ? 'expanded' : ''}`}>
                        {showExpandedView && (
                          <td className="expand-cell">
                            <button
                              onClick={() => toggleRowExpansion(log.id)}
                              className="expand-button"
                            >
                              {expandedRows[log.id] ? '▼' : '▶'}
                            </button>
                          </td>
                        )}
                        {state.selectedFields.map(field => (
                          <td key={`${log.id}-${field}`} className="field-cell">
                            {formatFieldValue(getFieldValue(log, field), field)}
                          </td>
                        ))}
                      </tr>
                      {showExpandedView && expandedRows[log.id] && (
                        <tr className="expanded-row">
                          <td colSpan={state.selectedFields.length + 1}>
                            <ExpandedRowDetails log={log} />
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {renderPagination()}
        </>
      )}
    </div>
  );
};

export default DiscoverTable;