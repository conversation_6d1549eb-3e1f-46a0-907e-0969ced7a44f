import React, { useState } from 'react';
import { ConfigurationEntry, SeverityLevel } from '../../types/configuration';
import { useConfigurationEntries } from '../../hooks';
import { getSeverityFromScore } from '../../utils/configurationUtils';

interface ConfigurationEntryDetailsProps {
  entry: ConfigurationEntry;
  onClose?: () => void;
}

/**
 * Component for displaying detailed information about a configuration entry
 */
const ConfigurationEntryDetails: React.FC<ConfigurationEntryDetailsProps> = ({ entry, onClose }) => {
  const { formatFieldValue } = useConfigurationEntries();
  const [activeTab, setActiveTab] = useState<'details' | 'remediation' | 'json'>('details');
  
  // Format JSON for display
  const formattedJson = JSON.stringify(entry, null, 2);
  
  // Get severity level
  const severityLevel = getSeverityFromScore(entry.score);
  
  // Get severity color
  const getSeverityColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case SeverityLevel.LOW:
        return '#4CAF50'; // Green
      case SeverityLevel.MEDIUM:
        return '#FF9800'; // Orange
      case SeverityLevel.HIGH:
        return '#F44336'; // Red
      case SeverityLevel.CRITICAL:
        return '#9C27B0'; // Purple
      default:
        return 'inherit';
    }
  };
  
  // Get result color
  const getResultColor = (result: string): string => {
    switch (result) {
      case 'passed':
        return '#4CAF50'; // Green
      case 'failed':
        return '#F44336'; // Red
      case 'not_applicable':
        return '#9E9E9E'; // Gray
      default:
        return 'inherit';
    }
  };
  
  // Get all fields from the entry for the details tab
  const getEntryFields = () => {
    const fields: Array<{ key: string; value: unknown }> = [];
    
    // Add base fields
    fields.push({ key: 'timestamp', value: entry.timestamp });
    fields.push({ key: 'result', value: entry.result });
    fields.push({ key: 'score', value: entry.score });
    fields.push({ key: 'severity', value: severityLevel });
    fields.push({ key: 'component', value: entry.component });
    fields.push({ key: 'configuration', value: entry.configuration });
    
    // Add agent fields
    fields.push({ key: 'agent.id', value: entry.agent.id });
    fields.push({ key: 'agent.name', value: entry.agent.name });
    fields.push({ key: 'agent.ip', value: entry.agent.ip });
    
    // Add rule fields
    fields.push({ key: 'rule.id', value: entry.rule.id });
    fields.push({ key: 'rule.description', value: entry.rule.description });
    fields.push({ key: 'rule.level', value: entry.rule.level });
    fields.push({ key: 'rule.groups', value: entry.rule.groups });
    
    // Add check fields
    fields.push({ key: 'check.id', value: entry.check.id });
    fields.push({ key: 'check.title', value: entry.check.title });
    fields.push({ key: 'check.description', value: entry.check.description });
    fields.push({ key: 'check.compliance', value: entry.check.compliance });
    
    return fields;
  };
  
  
  
  const detailsFields = getEntryFields();
  
  
  return (
    <div 
      role="region"
      aria-label="Configuration entry details"
      style={{
        padding: '16px',
        background: 'rgba(0, 0, 0, 0.2)',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        color: 'white',
        fontSize: '14px',
      }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px',
      }}>
        <h4 style={{ margin: 0 }}>Configuration Details</h4>
        
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          {/* Result badge */}
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            background: getResultColor(entry.result),
            color: 'white',
            fontWeight: 'bold',
            fontSize: '12px',
            textTransform: 'uppercase',
          }}>
            {entry.result}
          </div>
          
          {/* Severity badge */}
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            background: getSeverityColor(severityLevel),
            color: 'white',
            fontWeight: 'bold',
            fontSize: '12px',
            textTransform: 'uppercase',
          }}>
            {severityLevel}
          </div>
          
          {/* Tab navigation */}
          <div role="tablist" style={{ display: 'flex', gap: '8px' }}>
            <button
              role="tab"
              aria-selected={activeTab === 'details'}
              aria-controls="details-panel"
              id="details-tab"
              onClick={() => setActiveTab('details')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'details' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'details' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              Details
            </button>
            <button
              role="tab"
              aria-selected={activeTab === 'remediation'}
              aria-controls="remediation-panel"
              id="remediation-tab"
              onClick={() => setActiveTab('remediation')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'remediation' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'remediation' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              Remediation
            </button>
            <button
              role="tab"
              aria-selected={activeTab === 'json'}
              aria-controls="json-panel"
              id="json-tab"
              onClick={() => setActiveTab('json')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'json' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'json' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              JSON
            </button>
          </div>
          
          {/* Close button */}
          {onClose && (
            <button
              onClick={onClose}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'rgba(255, 255, 255, 0.7)',
                cursor: 'pointer',
                padding: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M18 6L6 18M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>
      
      {/* Details tab */}
      <div 
        role="tabpanel"
        id="details-panel"
        aria-labelledby="details-tab"
        hidden={activeTab !== 'details'}
        style={{
          display: activeTab === 'details' ? 'grid' : 'none',
          gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
          gap: '8px',
          maxHeight: '400px',
          overflowY: 'auto',
        }}
      >
        {detailsFields.map(({ key, value }) => (
          <React.Fragment key={key}>
            <div style={{ 
              color: 'rgba(255, 255, 255, 0.7)',
              fontWeight: 'bold',
              padding: '4px 8px',
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
            }}>
              {key}
            </div>
            <div style={{
              padding: '4px 8px',
              wordBreak: 'break-word',
            }}>
              {formatFieldValue(value, key)}
            </div>
          </React.Fragment>
        ))}
      </div>
      
      {/* Remediation tab */}
      <div 
        role="tabpanel"
        id="remediation-panel"
        aria-labelledby="remediation-tab"
        hidden={activeTab !== 'remediation'}
        style={{
          display: activeTab === 'remediation' ? 'flex' : 'none',
          flexDirection: 'column',
          gap: '16px',
          maxHeight: '400px',
          overflowY: 'auto',
        }}
      >
        <div style={{
          padding: '12px',
          background: 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
        }}>
          <h5 style={{ margin: '0 0 8px 0', color: '#00e5ff' }}>Rationale</h5>
          <p style={{ margin: 0, lineHeight: '1.5' }}>{entry.check.rationale}</p>
        </div>
        
        <div style={{
          padding: '12px',
          background: 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
          border: '1px solid rgba(0, 229, 255, 0.3)',
        }}>
          <h5 style={{ margin: '0 0 8px 0', color: '#00e5ff' }}>Remediation Steps</h5>
          <p style={{ margin: 0, lineHeight: '1.5' }}>{entry.check.remediation}</p>
        </div>
        
        {Object.keys(entry.data).length > 0 && (
          <div style={{
            padding: '12px',
            background: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
          }}>
            <h5 style={{ margin: '0 0 8px 0', color: '#00e5ff' }}>Additional Data</h5>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
              gap: '8px',
            }}>
              {Object.entries(entry.data).map(([key, value]) => (
                <React.Fragment key={key}>
                  <div style={{ 
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontWeight: 'bold',
                    padding: '4px 8px',
                    background: 'rgba(0, 0, 0, 0.2)',
                    borderRadius: '4px',
                  }}>
                    {key}
                  </div>
                  <div style={{
                    padding: '4px 8px',
                    wordBreak: 'break-word',
                  }}>
                    {formatFieldValue(value, `data.${key}`)}
                  </div>
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
        
        {entry.check.compliance.length > 0 && (
          <div style={{
            padding: '12px',
            background: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
          }}>
            <h5 style={{ margin: '0 0 8px 0', color: '#00e5ff' }}>Compliance Standards</h5>
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '8px',
            }}>
              {entry.check.compliance.map(standard => (
                <div key={standard} style={{
                  padding: '4px 8px',
                  background: 'rgba(0, 229, 255, 0.1)',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                }}>
                  {standard}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* JSON tab */}
      <pre 
        role="tabpanel"
        id="json-panel"
        aria-labelledby="json-tab"
        hidden={activeTab !== 'json'}
        style={{
          display: activeTab === 'json' ? 'block' : 'none',
          background: 'rgba(0, 0, 0, 0.3)',
          padding: '12px',
          borderRadius: '4px',
          overflowX: 'auto',
          maxHeight: '400px',
          fontSize: '12px',
          fontFamily: 'monospace',
          whiteSpace: 'pre-wrap',
        }}
      >
        {formattedJson}
      </pre>
    </div>
  );
};

export default ConfigurationEntryDetails;