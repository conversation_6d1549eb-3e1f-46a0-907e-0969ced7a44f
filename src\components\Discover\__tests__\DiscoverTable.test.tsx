import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import DiscoverTable from '../DiscoverTable';
import { DiscoverProvider, useDiscover, discoverActions } from '../../../context/DiscoverContext';
import { DataPluginProvider } from '../../../context/DataPluginContext';
import { useSearch, useQuery } from '../../../hooks/dataHooks';
import { useFieldFormat } from '../../../hooks/uiHooks';
import { smallSampleLogData } from '../../../data/sampleLogData';

// Mock the hooks
jest.mock('../../../hooks/dataHooks', () => {
  const originalModule = jest.requireActual('../../../hooks/dataHooks');
  
  return {
    ...originalModule,
    useSearch: jest.fn(),
    useQuery: jest.fn()
  };
});

jest.mock('../../../hooks/uiHooks', () => {
  const originalModule = jest.requireActual('../../../hooks/uiHooks');
  
  return {
    ...originalModule,
    useFieldFormat: jest.fn()
  };
});

// Mock the useDiscover hook
jest.mock('../../../context/DiscoverContext', () => {
  const originalModule = jest.requireActual('../../../context/DiscoverContext');
  
  return {
    ...originalModule,
    useDiscover: jest.fn()
  };
});

describe('DiscoverTable', () => {
  const mockDispatch = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    (useSearch as jest.Mock).mockReturnValue({
      results: {
        hits: {
          total: 10,
          hits: [
            { 
              _id: '1', 
              _source: { 
                timestamp: new Date().toISOString(),
                source: 'test-source',
                message: 'Test message',
                level: 'info'
              } 
            }
          ]
        },
        took: 5,
        timed_out: false
      },
      isLoading: false,
      error: null
    });
    
    (useQuery as jest.Mock).mockReturnValue({
      query: { query: 'test query', language: 'kuery' }
    });
    
    (useFieldFormat as jest.Mock).mockReturnValue({
      formatValue: (value: unknown) => `Formatted: ${value}`
    });
    
    (useDiscover as jest.Mock).mockReturnValue({
      state: {
        filteredData: smallSampleLogData,
        selectedFields: ['timestamp', 'source', 'message', 'level'],
        pagination: {
          currentPage: 1,
          pageSize: 10,
          totalItems: smallSampleLogData.length
        }
      },
      dispatch: mockDispatch
    });
  });
  
  it('should render the table with data', () => {
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Check that the table is rendered
    expect(screen.getByRole('table')).toBeInTheDocument();
    
    // Check that the table header has the selected fields
    const headerCells = screen.getAllByRole('columnheader');
    expect(headerCells.length).toBe(5); // 4 fields + expand column
    expect(headerCells[1]).toHaveTextContent('timestamp');
    expect(headerCells[2]).toHaveTextContent('source');
    expect(headerCells[3]).toHaveTextContent('message');
    expect(headerCells[4]).toHaveTextContent('level');
    
    // Check that the table has rows
    const rows = screen.getAllByRole('row');
    expect(rows.length).toBeGreaterThan(1); // Header + data rows
  });
  
  it('should show loading state', () => {
    (useSearch as jest.Mock).mockReturnValue({
      results: null,
      isLoading: true,
      error: null
    });
    
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Check that loading state is shown
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // Check that the table is not rendered
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
  });
  
  it('should show error state', () => {
    (useSearch as jest.Mock).mockReturnValue({
      results: null,
      isLoading: false,
      error: new Error('Test error')
    });
    
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Check that error state is shown
    expect(screen.getByText('Error: Test error')).toBeInTheDocument();
    
    // Check that the table is not rendered
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
  });
  
  it('should show empty state', () => {
    (useDiscover as jest.Mock).mockReturnValue({
      state: {
        filteredData: [],
        selectedFields: ['timestamp', 'source', 'message', 'level'],
        pagination: {
          currentPage: 1,
          pageSize: 10,
          totalItems: 0
        }
      },
      dispatch: mockDispatch
    });
    
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Check that empty state is shown
    expect(screen.getByText('No data found')).toBeInTheDocument();
    
    // Check that the table is not rendered
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
  });
  
  it('should handle pagination', () => {
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Check that pagination is rendered
    expect(screen.getByText('Page 1 of')).toBeInTheDocument();
    
    // Click next page button
    fireEvent.click(screen.getByText('Next'));
    
    // Check that dispatch was called with the correct action
    expect(mockDispatch).toHaveBeenCalledWith(discoverActions.setCurrentPage(2));
    
    // Click previous page button
    fireEvent.click(screen.getByText('Previous'));
    
    // Check that dispatch was called with the correct action
    expect(mockDispatch).toHaveBeenCalledWith(discoverActions.setCurrentPage(0));
    
    // Click last page button
    fireEvent.click(screen.getByText('Last'));
    
    // Check that dispatch was called with the correct action
    expect(mockDispatch).toHaveBeenCalledWith(discoverActions.setCurrentPage(5));
    
    // Click first page button
    fireEvent.click(screen.getByText('First'));
    
    // Check that dispatch was called with the correct action
    expect(mockDispatch).toHaveBeenCalledWith(discoverActions.setCurrentPage(1));
  });
  
  it('should handle page size change', () => {
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Change page size
    const pageSizeSelect = screen.getByRole('combobox');
    fireEvent.change(pageSizeSelect, { target: { value: '25' } });
    
    // Check that dispatch was called with the correct action
    expect(mockDispatch).toHaveBeenCalledWith(discoverActions.setPageSize(25));
  });
  
  it('should expand and collapse rows', () => {
    render(
      <DataPluginProvider>
        <DiscoverProvider>
          <DiscoverTable />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Get expand buttons
    const expandButtons = screen.getAllByRole('button', { name: /▶/ });
    expect(expandButtons.length).toBeGreaterThan(0);
    
    // Click the first expand button
    fireEvent.click(expandButtons[0]);
    
    // Check that the row is expanded
    expect(screen.getByText('Log Details')).toBeInTheDocument();
    
    // Click the expand button again (now showing ▼)
    const collapseButton = screen.getByRole('button', { name: /▼/ });
    fireEvent.click(collapseButton);
    
    // Check that the row is collapsed
    expect(screen.queryByText('Log Details')).not.toBeInTheDocument();
  });
});