import React, { useState } from 'react';
import DiscoverTable from '../discover/DiscoverTable';
import QueryEditor from '../query/QueryEditor';
import DatasetSelector from '../dataset/DatasetSelector';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';
import { useDataPlugin } from '../../context/DataPluginContext';
import { useSearch, useQuery, useDataset } from '../../hooks/dataHooks';
import { Query, Dataset } from '../../data/plugin/interfaces';

/**
 * Example component that demonstrates how to use the DiscoverTable
 */
const DiscoverTableExample: React.FC = () => {
  const { state, dispatch } = useDiscover();
  const dataPlugin = useDataPlugin();
  useSearch();
  const { query } = useQuery();
  const { selectedDataset } = useDataset();
  
  const [showDatasetSelector, setShowDatasetSelector] = useState(false);
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  
  // Handle query submit
  const handleQuerySubmit = (query: Query) => {
    console.log('Query submitted:', query);
  };
  
  // Handle dataset selection
  const handleDatasetSelected = (dataset: Dataset) => {
    console.log('Dataset selected:', dataset);
    setShowDatasetSelector(false);
    
    // Get available fields for the dataset
    if (dataPlugin) {
      const datasetService = dataPlugin.getQueryService().getDatasetService();
      const typeConfig = datasetService.getType(dataset.type);
      
      if (typeConfig) {
        typeConfig.fetchFields(dataset).then(fields => {
          setAvailableFields(fields.map(field => field.name));
        });
      }
    }
  };
  
  // Toggle field selection
  const toggleField = (field: string) => {
    dispatch(discoverActions.toggleField(field));
  };
  
  // Handle time range change
  const handleTimeRangeChange = (preset: string) => {
    const now = new Date();
    let from: Date;
    
    switch (preset) {
      case 'last-15m':
        from = new Date(now.getTime() - (15 * 60 * 1000));
        break;
      case 'last-1h':
        from = new Date(now.getTime() - (60 * 60 * 1000));
        break;
      case 'last-24h':
        from = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        break;
      case 'last-7d':
        from = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        break;
      case 'last-30d':
        from = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        break;
      default:
        from = new Date(now.getTime() - (24 * 60 * 60 * 1000));
    }
    
    dispatch(discoverActions.setTimeRange(from, now, preset));
  };
  
  return (
    <div className="discover-table-example">
      <h2>Discover Table Example</h2>
      
      <div className="controls-container">
        <div className="dataset-controls">
          <button 
            onClick={() => setShowDatasetSelector(!showDatasetSelector)}
            className="dataset-button"
          >
            {showDatasetSelector ? 'Hide Dataset Selector' : 'Select Dataset'}
          </button>
          
          {selectedDataset && (
            <div className="selected-dataset">
              <span className="dataset-label">Dataset:</span>
              <span className="dataset-value">{selectedDataset.title}</span>
            </div>
          )}
        </div>
        
        <div className="time-range-controls">
          <span className="time-range-label">Time Range:</span>
          <div className="time-range-buttons">
            <button onClick={() => handleTimeRangeChange('last-15m')}>Last 15m</button>
            <button onClick={() => handleTimeRangeChange('last-1h')}>Last 1h</button>
            <button onClick={() => handleTimeRangeChange('last-24h')}>Last 24h</button>
            <button onClick={() => handleTimeRangeChange('last-7d')}>Last 7d</button>
            <button onClick={() => handleTimeRangeChange('last-30d')}>Last 30d</button>
          </div>
        </div>
      </div>
      
      {showDatasetSelector && (
        <div className="dataset-selector-container">
          <DatasetSelector onDatasetSelected={handleDatasetSelected} />
        </div>
      )}
      
      <div className="query-editor-container">
        <QueryEditor
          initialQuery={query.query}
          initialLanguage={query.language}
          onSubmit={handleQuerySubmit}
          placeholder="Enter a query..."
          autoFocus={false}
        />
      </div>
      
      <div className="example-container">
        <div className="fields-container">
          <h3>Available Fields</h3>
          <div className="fields-list">
            {availableFields.length > 0 ? (
              <div className="field-checkboxes">
                {availableFields.map(field => (
                  <div key={field} className="field-checkbox">
                    <label>
                      <input
                        type="checkbox"
                        checked={state.selectedFields.includes(field)}
                        onChange={() => toggleField(field)}
                      />
                      {field}
                    </label>
                  </div>
                ))}
              </div>
            ) : (
              <p>No fields available</p>
            )}
          </div>
        </div>
        
        <div className="table-container">
          <h3>Discover Table</h3>
          <DiscoverTable
            pageSize={10}
            showPagination={true}
            showExpandedView={true}
          />
        </div>
      </div>
      
      <div className="usage-info">
        <h3>How to Use</h3>
        <pre>{`
import DiscoverTable from '../discover/DiscoverTable';
import { DiscoverProvider } from '../../context/DiscoverContext';
import { DataPluginProvider } from '../../context/DataPluginContext';

const MyComponent: React.FC = () => {
  return (
    <DataPluginProvider>
      <DiscoverProvider>
        <DiscoverTable
          pageSize={10}
          showPagination={true}
          showExpandedView={true}
        />
      </DiscoverProvider>
    </DataPluginProvider>
  );
};
        `}</pre>
      </div>
    </div>
  );
};

export default DiscoverTableExample;