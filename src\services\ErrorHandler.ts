/**
 * Error categories for better error handling
 */
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown'
}

/**
 * Interface for structured error information
 */
export interface ErrorInfo {
  message: string;
  category: ErrorCategory;
  code?: string;
  details?: Record<string, unknown>;
  originalError?: Error;
}

/**
 * Custom error class with additional information
 */
export class AppError extends Error {
  category: ErrorCategory;
  code?: string;
  details?: Record<string, unknown>;
  originalError?: Error;
  
  constructor(info: ErrorInfo) {
    super(info.message);
    this.name = 'AppError';
    this.category = info.category;
    this.code = info.code;
    this.details = info.details;
    this.originalError = info.originalError;
    
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, AppError.prototype);
  }
}

/**
 * Central error handler service
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorListeners: Array<(error: ErrorInfo) => void> = [];
  
  private constructor() {
    // Private constructor to enforce singleton pattern
    this.setupGlobalHandlers();
  }
  
  /**
   * Gets the singleton instance of the ErrorHandler
   */
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }
  
  /**
   * Sets up global error handlers
   */
  private setupGlobalHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason);
    });
    
    // Handle global errors
    window.addEventListener('error', (event) => {
      this.handleError(event.error || new Error(event.message));
    });
  }
  
  /**
   * Handles an error by categorizing it and notifying listeners
   * @param error The error to handle
   * @returns Structured error information
   */
  public handleError(error: unknown): ErrorInfo {
    const errorInfo = this.categorizeError(error);
    
    // Log the error
    console.error('Error handled:', errorInfo);
    
    // Notify listeners
    this.notifyListeners(errorInfo);
    
    return errorInfo;
  }
  
  /**
   * Categorizes an error and extracts relevant information
   * @param error The error to categorize
   * @returns Structured error information
   */
  private categorizeError(error: unknown): ErrorInfo {
    // If it's already an AppError, return its info
    if (error instanceof AppError) {
      return {
        message: error.message,
        category: error.category,
        code: error.code,
        details: error.details,
        originalError: error.originalError
      };
    }
    
    // If it's a standard Error, categorize it
    if (error instanceof Error) {
      const message = error.message;
      let category = ErrorCategory.UNKNOWN;
      let code: string | undefined;
      
      // Network errors
      if (
        message.includes('Network Error') ||
        message.includes('Failed to fetch') ||
        message.includes('timeout') ||
        error.name === 'AbortError'
      ) {
        category = ErrorCategory.NETWORK;
        code = 'NETWORK_ERROR';
      }
      // Authentication errors
      else if (
        message.includes('401') ||
        message.includes('Unauthorized') ||
        message.includes('authentication')
      ) {
        category = ErrorCategory.AUTHENTICATION;
        code = 'AUTH_ERROR';
      }
      // Authorization errors
      else if (
        message.includes('403') ||
        message.includes('Forbidden') ||
        message.includes('permission')
      ) {
        category = ErrorCategory.AUTHORIZATION;
        code = 'PERMISSION_ERROR';
      }
      // Validation errors
      else if (
        message.includes('validation') ||
        message.includes('invalid') ||
        message.includes('required')
      ) {
        category = ErrorCategory.VALIDATION;
        code = 'VALIDATION_ERROR';
      }
      // Server errors
      else if (
        message.includes('500') ||
        message.includes('Internal Server Error') ||
        message.includes('502') ||
        message.includes('503') ||
        message.includes('504')
      ) {
        category = ErrorCategory.SERVER;
        code = 'SERVER_ERROR';
      }
      // Client errors
      else if (
        message.includes('TypeError') ||
        message.includes('ReferenceError') ||
        message.includes('SyntaxError')
      ) {
        category = ErrorCategory.CLIENT;
        code = 'CLIENT_ERROR';
      }
      
      return {
        message,
        category,
        code,
        originalError: error
      };
    }
    
    // If it's a string, create a simple error info
    if (typeof error === 'string') {
      return {
        message: error,
        category: ErrorCategory.UNKNOWN
      };
    }
    
    // For other types, create a generic error info
    return {
      message: 'An unknown error occurred',
      category: ErrorCategory.UNKNOWN,
      details: { error }
    };
  }
  
  /**
   * Adds an error listener
   * @param listener The listener function
   * @returns A function to remove the listener
   */
  public addListener(listener: (error: ErrorInfo) => void): () => void {
    this.errorListeners.push(listener);
    
    // Return a function to remove the listener
    return () => {
      this.errorListeners = this.errorListeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Notifies all listeners of an error
   * @param errorInfo The error information
   */
  private notifyListeners(errorInfo: ErrorInfo): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(errorInfo);
      } catch (listenerError) {
        console.error('Error in error listener:', listenerError);
      }
    });
  }
  
  /**
   * Creates a new AppError
   * @param info The error information
   * @returns A new AppError
   */
  public createError(info: ErrorInfo): AppError {
    return new AppError(info);
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();