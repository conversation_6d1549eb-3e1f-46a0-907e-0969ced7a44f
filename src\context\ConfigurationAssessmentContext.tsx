/**
 * Configuration Assessment Context
 * 
 * This context provides state management for the Configuration Assessment dashboard.
 * It follows the same pattern as the DiscoverContext but is adapted for configuration assessment data.
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { ConfigurationEntry } from '../types/configuration';
import { ConfigurationAssessmentState, ConfigurationAssessmentAction } from '../types/configurationAssessment';
import { defaultDataset } from '../data/configurationDatasets';
import { configurationAssessmentReducer, getInitialState } from './configurationAssessmentUtils';



/**
 * Create the Configuration Assessment Context
 */
const ConfigurationAssessmentContext = createContext<{
  state: ConfigurationAssessmentState;
  dispatch: React.Dispatch<ConfigurationAssessmentAction>;
} | undefined>(undefined);

/**
 * Configuration Assessment Provider Props interface
 */
interface ConfigurationAssessmentProviderProps {
  children: ReactNode;
  initialData?: ConfigurationEntry[];
}

/**
 * Configuration Assessment Provider component
 */
export const ConfigurationAssessmentProvider: React.FC<ConfigurationAssessmentProviderProps> = ({
  children,
  initialData = defaultDataset,
}) => {
  const [state, dispatch] = useReducer(configurationAssessmentReducer, {
    ...getInitialState(),
    isLoading: true,
  });

  // Load initial data with optimized performance
  useEffect(() => {
    const loadData = async () => {
      // Set loading state and clear any previous errors
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_PROCESSING_OPERATION', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      try {
        // In a real app, this would be an API call
        // For now, we're using the sample data
        
        // Use a shorter delay for better performance
        // This simulates a faster network response
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Process data in the next event loop tick to allow UI to update
        setTimeout(() => {
          dispatch({ type: 'SET_CONFIG_DATA', payload: initialData });
        }, 0);
      } catch (error) {
        console.error('Error loading configuration data:', error);
        dispatch({ type: 'SET_CONFIG_DATA', payload: [] });
        dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load configuration data' });
      }
    };

    loadData();
  }, [initialData]);

  // Set up auto-refresh if enabled
  useEffect(() => {
    if (!state.autoRefresh) return;

    const intervalId = setInterval(() => {
      // In a real app, this would fetch fresh data
      // For demo purposes, we'll just refresh with the same data
      dispatch({ type: 'SET_CONFIG_DATA', payload: state.configData });
    }, state.refreshInterval);

    return () => clearInterval(intervalId);
  }, [state.autoRefresh, state.refreshInterval, state.configData]);

  const value = { state, dispatch };

  return (
    <ConfigurationAssessmentContext.Provider value={value}>
      {children}
    </ConfigurationAssessmentContext.Provider>
  );
};

/**
 * Custom hook for using the Configuration Assessment context
 */
export const useConfigurationAssessment = () => {
  const context = useContext(ConfigurationAssessmentContext);

  if (context === undefined) {
    throw new Error(
      'useConfigurationAssessment must be used within a ConfigurationAssessmentProvider'
    );
  }

  return context;
};

// Export action creators from separate file
