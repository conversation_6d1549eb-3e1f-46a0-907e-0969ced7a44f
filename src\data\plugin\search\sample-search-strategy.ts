import { BaseSearchStrategy } from './base-search-strategy';
import { SearchRequest, SearchResponse } from '../interfaces';

/**
 * A sample search strategy that demonstrates how to implement a search strategy
 * for a specific data source.
 * 
 * This strategy generates sample data for testing and development purposes.
 */
export class SampleSearchStrategy extends BaseSearchStrategy {
  /**
   * The unique identifier for this search strategy
   */
  public id = 'sample';
  
  /**
   * Executes a search request against the sample data source
   * @param request The search request to execute
   * @param options Additional options for the search
   * @returns A promise that resolves to the search response
   */
  protected async executeSearch(request: SearchRequest): Promise<SearchResponse> {
    // In a real implementation, this would execute the search against a real data source
    // For now, we'll return some sample data
    
    // Simulate a network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Check if we should simulate an error (for testing)
    if (request.query.query.toLowerCase().includes('error')) {
      throw new Error('Simulated error in sample search strategy');
    }
    
    // Generate sample data based on the request
    const size = request.size || 10;
    const from = request.from || 0;
    
    // Create sample hits
    const hits = Array.from({ length: size }, (_, i) => {
      const index = i + from;
      const timestamp = new Date();
      timestamp.setMinutes(timestamp.getMinutes() - index); // Stagger timestamps
      
      return {
        _id: `doc_${index}`,
        _source: {
          timestamp: timestamp.toISOString(),
          message: `Sample log message ${index}`,
          level: index % 3 === 0 ? 'error' : index % 2 === 0 ? 'warning' : 'info',
          user: `user_${index % 5}`,
          bytes: Math.floor(Math.random() * 10000),
          host: `host-${index % 3}.example.com`,
          path: `/api/v1/${index % 10}`,
          method: index % 4 === 0 ? 'POST' : index % 3 === 0 ? 'PUT' : index % 2 === 0 ? 'DELETE' : 'GET',
          status: index % 5 === 0 ? 500 : index % 4 === 0 ? 400 : index % 3 === 0 ? 404 : 200,
          duration: Math.floor(Math.random() * 500)
        }
      };
    });
    
    // Return the sample response
    return {
      hits: {
        total: 100, // Simulate a total of 100 documents
        hits
      },
      took: Math.floor(Math.random() * 100), // Simulate random response time
      timed_out: false
    };
  }
}