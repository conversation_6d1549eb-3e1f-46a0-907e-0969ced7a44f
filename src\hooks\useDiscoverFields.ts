import { useMemo } from 'react';
import { useDiscover, discoverActions } from '../context/DiscoverContext';
import { DiscoverUtils } from '../utils/discoverUtils';

/**
 * Hook for managing fields and columns in the Discover interface
 */
export const useDiscoverFields = () => {
  const { state, dispatch } = useDiscover();
  const { selectedFields, logData } = state;
  
  // Get all available fields from the data
  const availableFields = useMemo(() => {
    return DiscoverUtils.getAvailableFields(logData);
  }, [logData]);
  
  // Get all field paths
  const fieldPaths = useMemo(() => {
    return DiscoverUtils.extractFieldPaths(logData);
  }, [logData]);
  
  // Get fields categorized by type
  const categorizedFields = useMemo(() => {
    return DiscoverUtils.categorizeFields(availableFields);
  }, [availableFields]);
  
  // Toggle field selection
  const toggleField = (field: string) => {
    dispatch(discoverActions.toggleField(field));
  };
  
  // Add field to selected fields
  const addField = (field: string) => {
    if (!selectedFields.includes(field)) {
      dispatch(discoverActions.toggleField(field));
    }
  };
  
  // Remove field from selected fields
  const removeField = (field: string) => {
    if (selectedFields.includes(field)) {
      dispatch(discoverActions.toggleField(field));
    }
  };
  
  // Reset to default fields
  const resetFields = () => {
    const defaultFields = ['timestamp', 'source', 'message', 'level', 'rule.description'];
    
    // Remove all current fields
    selectedFields.forEach(field => {
      dispatch(discoverActions.toggleField(field));
    });
    
    // Add default fields
    defaultFields.forEach(field => {
      if (!selectedFields.includes(field)) {
        dispatch(discoverActions.toggleField(field));
      }
    });
  };
  
  // Get field type
  const getFieldType = (value: unknown) => {
    return DiscoverUtils.getFieldType(value);
  };
  
  // Format field value for display
  const formatFieldValue = (value: unknown) => {
    return DiscoverUtils.formatFieldValue(value);
  };
  
  return {
    selectedFields,
    availableFields,
    fieldPaths,
    categorizedFields,
    toggleField,
    addField,
    removeField,
    resetFields,
    getFieldType,
    formatFieldValue,
  };
};