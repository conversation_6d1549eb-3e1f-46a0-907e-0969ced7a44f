import { DatasetService, Dataset, DatasetTypeConfig, DatasetField } from '../interfaces';

/**
 * Interface for cached dataset information
 */
interface CachedDataset {
  fields: DatasetField[];
  lastUpdated: number;
  expiresAt: number;
  metadata?: Record<string, unknown>;
}

/**
 * Implementation of the DatasetService interface
 * Responsible for managing datasets, dataset types, and caching dataset information
 */
export class DatasetServiceImpl implements DatasetService {
  private datasets: Dataset[] = [];
  private typeConfigs: Map<string, DatasetTypeConfig> = new Map();
  private datasetCache: Map<string, CachedDataset> = new Map();
  
  // Default cache expiration time in milliseconds (30 minutes)
  private readonly DEFAULT_CACHE_EXPIRATION = 30 * 60 * 1000;

  /**
   * Gets all available datasets
   * @returns Array of datasets
   */
  public getDatasets(): Dataset[] {
    return this.datasets;
  }

  /**
   * Gets a dataset by ID
   * @param id The dataset ID
   * @returns The dataset or undefined if not found
   */
  public getDataset(id: string): Dataset | undefined {
    return this.datasets.find(dataset => dataset.id === id);
  }

  /**
   * Registers a new dataset type configuration
   * @param config The dataset type configuration
   */
  public registerType(config: DatasetTypeConfig): void {
    if (!config || !config.id) {
      throw new Error('Invalid dataset type configuration');
    }
    
    this.typeConfigs.set(config.id, config);
    console.log(`Registered dataset type: ${config.id}`);
  }

  /**
   * Gets a dataset type configuration by type
   * @param type The dataset type
   * @returns The dataset type configuration or undefined if not found
   */
  public getType(type: string): DatasetTypeConfig | undefined {
    return this.typeConfigs.get(type);
  }

  /**
   * Gets all registered dataset types
   * @returns Array of dataset type configurations
   */
  public getTypes(): DatasetTypeConfig[] {
    return Array.from(this.typeConfigs.values());
  }

  /**
   * Caches a dataset for faster access
   * @param dataset The dataset to cache
   * @param services Services needed for caching
   * @param options Optional caching options
   */
  public async cacheDataset(
    dataset: Dataset, 
    services: unknown, 
    options?: { expiration?: number }
  ): Promise<void> {
    if (!dataset || !dataset.id) {
      throw new Error('Invalid dataset');
    }

    const typeConfig = dataset.type ? this.getType(dataset.type) : undefined;
    
    if (!typeConfig) {
      throw new Error(`Dataset type '${dataset.type}' not registered`);
    }
    
    try {
      // Fetch fields using the dataset type configuration
      const fields = await typeConfig.fetchFields(dataset);
      
      // Calculate expiration time
      const expiration = options?.expiration || this.DEFAULT_CACHE_EXPIRATION;
      const now = Date.now();
      
      // Store in cache
      this.datasetCache.set(dataset.id, {
        fields,
        lastUpdated: now,
        expiresAt: now + expiration,
        metadata: {
          supportedLanguages: typeConfig.supportedLanguages()
        }
      });
      
      console.log(`Cached dataset: ${dataset.id} with ${fields.length} fields`);
    } catch (error) {
      console.error(`Failed to cache dataset ${dataset.id}:`, error);
      throw error;
    }
  }

  /**
   * Gets cached fields for a dataset
   * @param datasetId The dataset ID
   * @returns Array of dataset fields or undefined if not cached
   */
  public getCachedFields(datasetId: string): DatasetField[] | undefined {
    const cached = this.datasetCache.get(datasetId);
    
    if (!cached) {
      return undefined;
    }
    
    // Check if cache has expired
    if (Date.now() > cached.expiresAt) {
      console.log(`Cache expired for dataset: ${datasetId}`);
      return undefined;
    }
    
    return cached.fields;
  }

  /**
   * Checks if a dataset is cached
   * @param datasetId The dataset ID
   * @returns True if the dataset is cached and not expired
   */
  public isDatasetCached(datasetId: string): boolean {
    const cached = this.datasetCache.get(datasetId);
    
    if (!cached) {
      return false;
    }
    
    return Date.now() <= cached.expiresAt;
  }

  /**
   * Invalidates the cache for a dataset
   * @param datasetId The dataset ID
   */
  public invalidateCache(datasetId: string): void {
    this.datasetCache.delete(datasetId);
    console.log(`Cache invalidated for dataset: ${datasetId}`);
  }

  /**
   * Adds a dataset to the available datasets
   * @param dataset The dataset to add
   */
  public addDataset(dataset: Dataset): void {
    if (!dataset || !dataset.id) {
      throw new Error('Invalid dataset');
    }
    
    if (!this.datasets.some(d => d.id === dataset.id)) {
      this.datasets.push(dataset);
      console.log(`Added dataset: ${dataset.id}`);
    }
  }

  /**
   * Removes a dataset from the available datasets
   * @param datasetId The dataset ID to remove
   */
  public removeDataset(datasetId: string): void {
    const initialLength = this.datasets.length;
    this.datasets = this.datasets.filter(d => d.id !== datasetId);
    
    if (this.datasets.length < initialLength) {
      // Also remove from cache if it exists
      this.invalidateCache(datasetId);
      console.log(`Removed dataset: ${datasetId}`);
    }
  }

  /**
   * Creates a dataset from a path using the appropriate dataset type
   * @param type The dataset type
   * @param path The path to the dataset
   * @returns The created dataset
   */
  public createDataset(type: string, path: unknown[]): Dataset | undefined {
    const typeConfig = this.getType(type);
    
    if (!typeConfig) {
      console.error(`Dataset type '${type}' not registered`);
      return undefined;
    }
    
    try {
      return typeConfig.toDataset(path);
    } catch (error) {
      console.error(`Failed to create dataset of type '${type}':`, error);
      return undefined;
    }
  }
}