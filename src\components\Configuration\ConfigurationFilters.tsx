import React, { useState } from 'react';
import { useConfigurationSearch } from '../../hooks/useConfigurationSearch';
import { SeverityLevel } from '../../types/configuration';

/**
 * Component for configuration-specific filters
 * Provides filters for result status, compliance standards, and severity
 * 
 * Requirements: 2.3, 2.4
 */
const ConfigurationFilters: React.FC = () => {
  const { 
    addFilter, 
    removeFilter, 
    appliedFilters,
    resultDistribution,
    complianceDistribution,
    severityDistribution
  } = useConfigurationSearch();
  
  // State for expanded filter sections
  const [expandedSections, setExpandedSections] = useState({
    result: true,
    severity: true,
    compliance: true
  });
  
  // Toggle section expansion
  const toggleSection = (section: 'result' | 'severity' | 'compliance') => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };
  
  // Check if a filter is applied
  const isFilterApplied = (field: string, value: unknown): boolean => {
    return appliedFilters.some(filter => 
      filter.field.toString() === field && 
      (filter.value === value || 
        (Array.isArray(filter.value) && filter.value.includes(value)))
    );
  };
  
  // Toggle a filter
  const toggleFilter = (field: string, value: unknown) => {
    if (isFilterApplied(field, value)) {
      removeFilter(field);
    } else {
      addFilter(field, value);
    }
  };
  
  // Get severity color
  const getSeverityColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case SeverityLevel.LOW:
        return '#4CAF50'; // Green
      case SeverityLevel.MEDIUM:
        return '#FF9800'; // Orange
      case SeverityLevel.HIGH:
        return '#F44336'; // Red
      case SeverityLevel.CRITICAL:
        return '#9C27B0'; // Purple
      default:
        return 'inherit';
    }
  };
  
  // Get result color
  const getResultColor = (result: string): string => {
    switch (result) {
      case 'passed':
        return '#4CAF50'; // Green
      case 'failed':
        return '#F44336'; // Red
      case 'not_applicable':
        return '#9E9E9E'; // Gray
      default:
        return 'inherit';
    }
  };
  
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '16px',
    }}>
      {/* Result Status Filter */}
      <div style={{
        background: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '4px',
        overflow: 'hidden',
      }}>
        <div 
          onClick={() => toggleSection('result')}
          style={{
            padding: '12px 16px',
            background: 'rgba(0, 229, 255, 0.05)',
            cursor: 'pointer',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <h4 style={{ margin: 0, fontSize: '14px', color: 'white' }}>
            Result Status
          </h4>
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2"
            style={{
              transform: expandedSections.result ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s',
              color: 'rgba(255, 255, 255, 0.7)',
            }}
          >
            <polyline points="6 9 12 15 18 9" />
          </svg>
        </div>
        
        {expandedSections.result && (
          <div style={{ padding: '8px 16px 16px' }}>
            {Object.entries(resultDistribution).map(([result, count]) => (
              <div 
                key={result}
                onClick={() => toggleFilter('result', result)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 0',
                  cursor: 'pointer',
                }}
              >
                <div style={{
                  width: '16px',
                  height: '16px',
                  borderRadius: '2px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: isFilterApplied('result', result) ? 'rgba(0, 229, 255, 0.2)' : 'transparent',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  {isFilterApplied('result', result) && (
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  )}
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  flex: 1,
                }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      background: getResultColor(result),
                      marginRight: '8px',
                    }} />
                    <span style={{ color: 'white', textTransform: 'capitalize' }}>
                      {result.replace('_', ' ')}
                    </span>
                  </div>
                  <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                    {count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Severity Filter */}
      <div style={{
        background: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '4px',
        overflow: 'hidden',
      }}>
        <div 
          onClick={() => toggleSection('severity')}
          style={{
            padding: '12px 16px',
            background: 'rgba(0, 229, 255, 0.05)',
            cursor: 'pointer',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <h4 style={{ margin: 0, fontSize: '14px', color: 'white' }}>
            Severity
          </h4>
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2"
            style={{
              transform: expandedSections.severity ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s',
              color: 'rgba(255, 255, 255, 0.7)',
            }}
          >
            <polyline points="6 9 12 15 18 9" />
          </svg>
        </div>
        
        {expandedSections.severity && (
          <div style={{ padding: '8px 16px 16px' }}>
            {Object.entries(severityDistribution).map(([severity, count]) => (
              <div 
                key={severity}
                onClick={() => toggleFilter('severity', severity)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 0',
                  cursor: 'pointer',
                }}
              >
                <div style={{
                  width: '16px',
                  height: '16px',
                  borderRadius: '2px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: isFilterApplied('severity', severity) ? 'rgba(0, 229, 255, 0.2)' : 'transparent',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  {isFilterApplied('severity', severity) && (
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  )}
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  flex: 1,
                }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      background: getSeverityColor(severity as SeverityLevel),
                      marginRight: '8px',
                    }} />
                    <span style={{ color: 'white', textTransform: 'capitalize' }}>
                      {severity}
                    </span>
                  </div>
                  <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                    {count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Compliance Standards Filter */}
      <div style={{
        background: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '4px',
        overflow: 'hidden',
      }}>
        <div 
          onClick={() => toggleSection('compliance')}
          style={{
            padding: '12px 16px',
            background: 'rgba(0, 229, 255, 0.05)',
            cursor: 'pointer',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <h4 style={{ margin: 0, fontSize: '14px', color: 'white' }}>
            Compliance Standards
          </h4>
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2"
            style={{
              transform: expandedSections.compliance ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s',
              color: 'rgba(255, 255, 255, 0.7)',
            }}
          >
            <polyline points="6 9 12 15 18 9" />
          </svg>
        </div>
        
        {expandedSections.compliance && (
          <div style={{ padding: '8px 16px 16px' }}>
            {Object.entries(complianceDistribution).map(([standard, count]) => (
              <div 
                key={standard}
                onClick={() => toggleFilter('check.compliance', standard)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 0',
                  cursor: 'pointer',
                }}
              >
                <div style={{
                  width: '16px',
                  height: '16px',
                  borderRadius: '2px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: isFilterApplied('check.compliance', standard) ? 'rgba(0, 229, 255, 0.2)' : 'transparent',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  {isFilterApplied('check.compliance', standard) && (
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                  )}
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  flex: 1,
                }}>
                  <span style={{ color: 'white' }}>
                    {standard}
                  </span>
                  <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                    {count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfigurationFilters;