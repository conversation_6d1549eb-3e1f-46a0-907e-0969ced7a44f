/**
 * Performance comparison utilities
 * 
 * This module provides utilities for comparing performance between
 * the Configuration Assessment and Discover pages.
 */

import { getAverageDuration } from './performanceTracking';

// Define operation categories for comparison
export enum OperationCategory {
  RENDERING = 'rendering',
  DATA_PROCESSING = 'data_processing',
  FILTERING = 'filtering',
  SORTING = 'sorting',
  PAGINATION = 'pagination',
  INTERACTION = 'interaction'
}

// Interface for performance comparison results
export interface PerformanceComparisonResult {
  operationName: string;
  category: OperationCategory;
  configAssessmentTime: number;
  discoverTime: number;
  difference: number;
  percentageDifference: number;
  isFaster: boolean;
  timestamp: number;
}

// Store for comparison results
const comparisonResults: PerformanceComparisonResult[] = [];
const MAX_COMPARISON_RESULTS = 50;

/**
 * Compare performance between Configuration Assessment and Discover pages
 * @param configOperation Name of the operation in Configuration Assessment
 * @param discoverOperation Name of the operation in Discover
 * @param category Operation category
 * @returns Comparison result
 */
export function comparePerformance(
  configOperation: string,
  discoverOperation: string,
  category: OperationCategory
): PerformanceComparisonResult {
  const configTime = getAverageDuration(configOperation);
  const discoverTime = getAverageDuration(discoverOperation);
  
  const difference = configTime - discoverTime;
  const percentageDifference = discoverTime > 0 
    ? (difference / discoverTime) * 100 
    : 0;
  
  const result: PerformanceComparisonResult = {
    operationName: configOperation,
    category,
    configAssessmentTime: configTime,
    discoverTime,
    difference,
    percentageDifference,
    isFaster: difference <= 0,
    timestamp: Date.now()
  };
  
  // Store the result
  addComparisonResult(result);
  
  return result;
}

/**
 * Add a comparison result to the store
 * @param result Comparison result
 */
function addComparisonResult(result: PerformanceComparisonResult): void {
  // Remove oldest result if we've reached the maximum
  if (comparisonResults.length >= MAX_COMPARISON_RESULTS) {
    comparisonResults.shift();
  }
  
  // Add new result
  comparisonResults.push(result);
}

/**
 * Get all comparison results
 * @param category Optional category filter
 * @returns Array of comparison results
 */
export function getComparisonResults(category?: OperationCategory): PerformanceComparisonResult[] {
  if (category) {
    return comparisonResults.filter(result => result.category === category);
  }
  return [...comparisonResults];
}

/**
 * Clear all comparison results
 */
export function clearComparisonResults(): void {
  comparisonResults.length = 0;
}

/**
 * Get performance summary comparing Configuration Assessment and Discover pages
 * @returns Summary object with performance metrics
 */
export function getPerformanceSummary() {
  const categories = Object.values(OperationCategory);
  const summary: Record<string, {
    totalOperations: number;
    fasterOperations: number;
    averageDifference: number;
    averagePercentageDifference: number;
    worstPerformer?: PerformanceComparisonResult;
    bestPerformer?: PerformanceComparisonResult;
  }> = {};
  
  // Initialize summary for each category
  categories.forEach(category => {
    summary[category] = {
      totalOperations: 0,
      fasterOperations: 0,
      averageDifference: 0,
      averagePercentageDifference: 0
    };
  });
  
  // Calculate summary metrics
  comparisonResults.forEach(result => {
    const categorySummary = summary[result.category];
    categorySummary.totalOperations++;
    
    if (result.isFaster) {
      categorySummary.fasterOperations++;
    }
    
    categorySummary.averageDifference += result.difference;
    categorySummary.averagePercentageDifference += result.percentageDifference;
    
    // Track worst performer
    if (!categorySummary.worstPerformer || 
        result.percentageDifference > categorySummary.worstPerformer.percentageDifference) {
      categorySummary.worstPerformer = result;
    }
    
    // Track best performer
    if (!categorySummary.bestPerformer || 
        result.percentageDifference < categorySummary.bestPerformer.percentageDifference) {
      categorySummary.bestPerformer = result;
    }
  });
  
  // Calculate averages
  categories.forEach(category => {
    const categorySummary = summary[category];
    if (categorySummary.totalOperations > 0) {
      categorySummary.averageDifference /= categorySummary.totalOperations;
      categorySummary.averagePercentageDifference /= categorySummary.totalOperations;
    }
  });
  
  return summary;
}

/**
 * Log performance comparison summary to console
 */
export function logPerformanceComparison(): void {
  const summary = getPerformanceSummary();
  
  console.group('Performance Comparison: Configuration Assessment vs Discover');
  
  Object.entries(summary).forEach(([category, metrics]) => {
    if (metrics.totalOperations === 0) return;
    
    console.group(`Category: ${category}`);
    console.log(`Total operations: ${metrics.totalOperations}`);
    console.log(`Operations where Configuration Assessment is faster: ${metrics.fasterOperations}`);
    console.log(`Average time difference: ${metrics.averageDifference.toFixed(2)}ms`);
    console.log(`Average percentage difference: ${metrics.averagePercentageDifference.toFixed(2)}%`);
    
    if (metrics.worstPerformer) {
      console.log(`Worst performer: ${metrics.worstPerformer.operationName} (${metrics.worstPerformer.percentageDifference.toFixed(2)}% slower)`);
    }
    
    if (metrics.bestPerformer) {
      console.log(`Best performer: ${metrics.bestPerformer.operationName} (${metrics.bestPerformer.percentageDifference.toFixed(2)}% ${metrics.bestPerformer.isFaster ? 'faster' : 'slower'})`);
    }
    
    console.groupEnd();
  });
  
  console.groupEnd();
}

/**
 * Create a performance comparison tracker for a specific component
 * @param configComponentName Name of the component in Configuration Assessment
 * @param discoverComponentName Name of the component in Discover
 * @returns Object with tracking methods
 */
export function createPerformanceComparisonTracker(
  configComponentName: string,
  discoverComponentName: string
) {
  return {
    compareOperation: (
      configOperationName: string,
      discoverOperationName: string,
      category: OperationCategory
    ): PerformanceComparisonResult => {
      return comparePerformance(
        `${configComponentName}.${configOperationName}`,
        `${discoverComponentName}.${discoverOperationName}`,
        category
      );
    },
    
    getComponentComparisons: () => {
      return comparisonResults.filter(result => 
        result.operationName.startsWith(`${configComponentName}.`)
      );
    },
    
    logComponentComparison: () => {
      const componentComparisons = comparisonResults.filter(result => 
        result.operationName.startsWith(`${configComponentName}.`)
      );
      
      if (componentComparisons.length === 0) {
        console.debug(`[Performance Comparison] No comparisons for ${configComponentName} vs ${discoverComponentName}`);
        return;
      }
      
      console.group(`[Performance Comparison] ${configComponentName} vs ${discoverComponentName}`);
      
      componentComparisons.forEach(comparison => {
        const status = comparison.isFaster ? '✅ FASTER' : '❌ SLOWER';
        console.log(
          `${status} | ${comparison.operationName.substring(configComponentName.length + 1)}: ` +
          `${comparison.configAssessmentTime.toFixed(2)}ms vs ${comparison.discoverTime.toFixed(2)}ms ` +
          `(${Math.abs(comparison.percentageDifference).toFixed(2)}% ${comparison.isFaster ? 'faster' : 'slower'})`
        );
      });
      
      console.groupEnd();
    }
  };
}