/**
 * Performance tracking utilities
 * 
 * This module provides utilities for measuring and tracking performance metrics
 * throughout the application.
 */

// Performance API polyfill for environments where it might not be available
export const perf = typeof performance !== 'undefined' ? performance : {
  now: () => Date.now()
};

// Store for performance metrics
interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  category?: string;
  metadata?: Record<string, unknown>;
}

const metrics: PerformanceMetric[] = [];
const MAX_METRICS = 200; // Increased maximum number of metrics to store

// Active timers for measuring ongoing operations
const activeTimers: Record<string, number> = {};

/**
 * Track the performance of a function
 * @param name Operation name
 * @param fn Function to measure
 * @param category Optional category for grouping metrics
 * @param metadata Optional additional data to store with the metric
 * @returns Result of the function
 */
export function trackPerformance<T>(
  name: string, 
  fn: () => T, 
  category?: string,
  metadata?: Record<string, unknown>
): T {
  const startTime = perf.now();
  const result = fn();
  const endTime = perf.now();
  const duration = endTime - startTime;
  
  // Store metric
  addMetric(name, duration, category, metadata);
  
  // Log in development mode
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV !== 'production') {
    console.debug(`[Performance] ${name}: ${duration.toFixed(2)}ms${category ? ` (${category})` : ''}`);
  }
  
  return result;
}

/**
 * Start timing an operation
 * @param name Operation name
 * @returns Timer ID (same as name)
 */
export function startTimer(name: string): string {
  activeTimers[name] = perf.now();
  return name;
}

/**
 * Stop timing an operation and record the metric
 * @param name Operation name (timer ID)
 * @param category Optional category for grouping metrics
 * @param metadata Optional additional data to store with the metric
 * @returns Duration in milliseconds, or -1 if timer not found
 */
export function stopTimer(
  name: string, 
  category?: string,
  metadata?: Record<string, unknown>
): number {
  if (activeTimers[name] === undefined) {
    console.warn(`[Performance] Timer '${name}' not found`);
    return -1;
  }
  
  const startTime = activeTimers[name];
  const endTime = perf.now();
  const duration = endTime - startTime;
  
  // Store metric
  addMetric(name, duration, category, metadata);
  
  // Clean up timer
  delete activeTimers[name];
  
  // Log in development mode
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV !== 'production') {
    console.debug(`[Performance] ${name}: ${duration.toFixed(2)}ms${category ? ` (${category})` : ''}`);
  }
  
  return duration;
}

/**
 * Add a performance metric to the store
 * @param name Operation name
 * @param duration Duration in milliseconds
 * @param category Optional category for grouping metrics
 * @param metadata Optional additional data to store with the metric
 */
function addMetric(
  name: string, 
  duration: number, 
  category?: string,
  metadata?: Record<string, unknown>
): void {
  // Remove oldest metric if we've reached the maximum
  if (metrics.length >= MAX_METRICS) {
    metrics.shift();
  }
  
  // Add new metric
  metrics.push({
    name,
    duration,
    timestamp: Date.now(),
    category,
    metadata
  });
}

/**
 * Get performance metrics for a specific operation
 * @param name Operation name
 * @param category Optional category filter
 * @returns Array of metrics for the operation
 */
export function getMetrics(name?: string, category?: string): PerformanceMetric[] {
  let result = [...metrics];
  
  if (name) {
    result = result.filter(metric => metric.name === name);
  }
  
  if (category) {
    result = result.filter(metric => metric.category === category);
  }
  
  return result;
}

/**
 * Get average duration for a specific operation
 * @param name Operation name
 * @param category Optional category filter
 * @returns Average duration in milliseconds
 */
export function getAverageDuration(name: string, category?: string): number {
  const operationMetrics = getMetrics(name, category);
  if (operationMetrics.length === 0) {
    return 0;
  }
  
  const totalDuration = operationMetrics.reduce((sum, metric) => sum + metric.duration, 0);
  return totalDuration / operationMetrics.length;
}

/**
 * Get median duration for a specific operation
 * @param name Operation name
 * @param category Optional category filter
 * @returns Median duration in milliseconds
 */
export function getMedianDuration(name: string, category?: string): number {
  const operationMetrics = getMetrics(name, category);
  if (operationMetrics.length === 0) {
    return 0;
  }
  
  const durations = operationMetrics.map(metric => metric.duration).sort((a, b) => a - b);
  const mid = Math.floor(durations.length / 2);
  
  return durations.length % 2 === 0
    ? (durations[mid - 1] + durations[mid]) / 2
    : durations[mid];
}

/**
 * Get performance statistics for a specific operation
 * @param name Operation name
 * @param category Optional category filter
 * @returns Statistics object with min, max, avg, median, and count
 */
export function getPerformanceStats(name: string, category?: string) {
  const operationMetrics = getMetrics(name, category);
  if (operationMetrics.length === 0) {
    return {
      min: 0,
      max: 0,
      avg: 0,
      median: 0,
      count: 0,
      total: 0
    };
  }
  
  const durations = operationMetrics.map(metric => metric.duration);
  const min = Math.min(...durations);
  const max = Math.max(...durations);
  const total = durations.reduce((sum, duration) => sum + duration, 0);
  const avg = total / durations.length;
  
  // Calculate median
  const sortedDurations = [...durations].sort((a, b) => a - b);
  const mid = Math.floor(sortedDurations.length / 2);
  const median = sortedDurations.length % 2 === 0
    ? (sortedDurations[mid - 1] + sortedDurations[mid]) / 2
    : sortedDurations[mid];
  
  return {
    min,
    max,
    avg,
    median,
    count: durations.length,
    total
  };
}

/**
 * Clear all stored metrics
 * @param category Optional category to clear (if omitted, clears all metrics)
 */
export function clearMetrics(category?: string): void {
  if (category) {
    let index = metrics.findIndex(metric => metric.category === category);
    while (index !== -1) {
      metrics.splice(index, 1);
      index = metrics.findIndex(metric => metric.category === category);
    }
  } else {
    metrics.length = 0;
  }
  
  // Also clear any active timers
  Object.keys(activeTimers).forEach(key => {
    delete activeTimers[key];
  });
}

/**
 * Create a performance tracker for a component
 * @param componentName Name of the component
 * @returns Object with tracking methods
 */
export function createPerformanceTracker(componentName: string) {
  return {
    /**
     * Track an operation with timing
     * @param operationName Name of the operation
     * @param fn Function to execute and time
     * @param category Optional category for grouping
     * @param metadata Optional metadata to store
     * @returns Result of the function
     */
    trackOperation: <T>(
      operationName: string, 
      fn: () => T, 
      category?: string,
      metadata?: Record<string, unknown>
    ): T => {
      return trackPerformance(`${componentName}.${operationName}`, fn, category, metadata);
    },
    
    /**
     * Start timing an operation
     * @param operationName Name of the operation
     * @returns Timer ID
     */
    startOperationTimer: (operationName: string): string => {
      return startTimer(`${componentName}.${operationName}`);
    },
    
    /**
     * Stop timing an operation
     * @param operationName Name of the operation
     * @param category Optional category for grouping
     * @param metadata Optional metadata to store
     * @returns Duration in milliseconds
     */
    stopOperationTimer: (
      operationName: string, 
      category?: string,
      metadata?: Record<string, unknown>
    ): number => {
      return stopTimer(`${componentName}.${operationName}`, category, metadata);
    },
    
    /**
     * Get all metrics for this component
     * @param operationName Optional operation name filter
     * @param category Optional category filter
     * @returns Array of metrics
     */
    getComponentMetrics: (operationName?: string, category?: string) => {
      let result = metrics.filter(metric => metric.name.startsWith(`${componentName}.`));
      
      if (operationName) {
        result = result.filter(metric => 
          metric.name === `${componentName}.${operationName}`
        );
      }
      
      if (category) {
        result = result.filter(metric => metric.category === category);
      }
      
      return result;
    },
    
    /**
     * Get statistics for a specific operation
     * @param operationName Name of the operation
     * @param category Optional category filter
     * @returns Statistics object
     */
    getOperationStats: (operationName: string, category?: string) => {
      return getPerformanceStats(`${componentName}.${operationName}`, category);
    },
    
    /**
     * Log performance metrics for this component
     * @param detailed Whether to show detailed statistics
     */
    logPerformance: (detailed: boolean = false) => {
      const componentMetrics = metrics.filter(metric => 
        metric.name.startsWith(`${componentName}.`)
      );
      
      if (componentMetrics.length === 0) {
        console.debug(`[Performance] No metrics for ${componentName}`);
        return;
      }
      
      console.group(`[Performance] ${componentName}`);
      
      // Group by operation name
      const operationGroups: Record<string, number[]> = {};
      componentMetrics.forEach(metric => {
        const operationName = metric.name.substring(componentName.length + 1);
        if (!operationGroups[operationName]) {
          operationGroups[operationName] = [];
        }
        operationGroups[operationName].push(metric.duration);
      });
      
      // Calculate and log statistics
      Object.entries(operationGroups).forEach(([operation, durations]) => {
        const total = durations.reduce((sum, duration) => sum + duration, 0);
        const average = total / durations.length;
        const min = Math.min(...durations);
        const max = Math.max(...durations);
        
        // Sort durations for median calculation
        const sortedDurations = [...durations].sort((a, b) => a - b);
        const mid = Math.floor(sortedDurations.length / 2);
        const median = sortedDurations.length % 2 === 0
          ? (sortedDurations[mid - 1] + sortedDurations[mid]) / 2
          : sortedDurations[mid];
        
        console.log(
          `${operation}: avg ${average.toFixed(2)}ms, ` +
          `median ${median.toFixed(2)}ms, ` +
          `min ${min.toFixed(2)}ms, ` +
          `max ${max.toFixed(2)}ms ` +
          `(${durations.length} samples)`
        );
        
        // Show detailed histogram for larger sample sizes
        if (detailed && durations.length >= 5) {
          console.group('Distribution');
          
          // Create simple histogram buckets
          const bucketSize = (max - min) / 5; // 5 buckets
          const buckets: Record<string, number> = {};
          
          durations.forEach(duration => {
            const bucketIndex = Math.min(4, Math.floor((duration - min) / bucketSize));
            const bucketStart = min + bucketIndex * bucketSize;
            const bucketEnd = bucketStart + bucketSize;
            const bucketLabel = `${bucketStart.toFixed(1)}-${bucketEnd.toFixed(1)}ms`;
            
            buckets[bucketLabel] = (buckets[bucketLabel] || 0) + 1;
          });
          
          Object.entries(buckets).forEach(([label, count]) => {
            const percentage = (count / durations.length) * 100;
            console.log(`${label}: ${'█'.repeat(Math.round(percentage / 5))} ${count} (${percentage.toFixed(1)}%)`);
          });
          
          console.groupEnd();
        }
      });
      
      console.groupEnd();
    }
  };
}

/**
 * Log all performance metrics to console
 * @param detailed Whether to show detailed statistics
 */
export function logAllPerformanceMetrics(_detailed: boolean = false): void {
  if (metrics.length === 0) {
    console.debug('[Performance] No metrics recorded');
    return;
  }
  
  console.group('[Performance] All Metrics');
  
  // Group by component
  const componentGroups: Record<string, PerformanceMetric[]> = {};
  
  metrics.forEach(metric => {
    const dotIndex = metric.name.indexOf('.');
    const componentName = dotIndex > 0 ? metric.name.substring(0, dotIndex) : 'global';
    
    if (!componentGroups[componentName]) {
      componentGroups[componentName] = [];
    }
    
    componentGroups[componentName].push(metric);
  });
  
  // Log each component's metrics
  Object.entries(componentGroups).forEach(([component, componentMetrics]) => {
    console.group(component);
    
    // Group by operation
    const operationGroups: Record<string, number[]> = {};
    
    componentMetrics.forEach(metric => {
      const operationName = metric.name.includes('.')
        ? metric.name.substring(metric.name.indexOf('.') + 1)
        : metric.name;
      
      if (!operationGroups[operationName]) {
        operationGroups[operationName] = [];
      }
      
      operationGroups[operationName].push(metric.duration);
    });
    
    // Log each operation's metrics
    Object.entries(operationGroups).forEach(([operation, durations]) => {
      const total = durations.reduce((sum, duration) => sum + duration, 0);
      const average = total / durations.length;
      const min = Math.min(...durations);
      const max = Math.max(...durations);
      
      // Calculate median
      const sortedDurations = [...durations].sort((a, b) => a - b);
      const mid = Math.floor(sortedDurations.length / 2);
      const median = sortedDurations.length % 2 === 0
        ? (sortedDurations[mid - 1] + sortedDurations[mid]) / 2
        : sortedDurations[mid];
      
      console.log(
        `${operation}: avg ${average.toFixed(2)}ms, ` +
        `median ${median.toFixed(2)}ms, ` +
        `min ${min.toFixed(2)}ms, ` +
        `max ${max.toFixed(2)}ms ` +
        `(${durations.length} samples)`
      );
    });
    
    console.groupEnd();
  });
  
  console.groupEnd();
}