import { lastValue<PERSON>rom } from 'rxjs';
import { IndexPatternInterceptor } from '../index-pattern-interceptor';
import { BaseSearchInterceptor } from '../base-search-interceptor';
import { IndexPatternService } from '../../services/index-pattern-service';
import { SearchRequest, Dataset } from '../../interfaces';

// Mock the IndexPatternService
jest.mock('../../services/index-pattern-service');

describe('IndexPatternInterceptor', () => {
  let interceptor: IndexPatternInterceptor;
  let baseInterceptor: BaseSearchInterceptor;
  let indexPatternService: jest.Mocked<IndexPatternService>;
  
  beforeEach(() => {
    // Create a mock IndexPatternService
    indexPatternService = new IndexPatternService() as jest.Mocked<IndexPatternService>;
    
    // Mock the getOrCreatePattern method
    indexPatternService.getOrCreatePattern = jest.fn().mockImplementation(async (dataset) => {
      return {
        id: `temp_${dataset.id}`,
        title: dataset.title,
        timeFieldName: dataset.timeFieldName,
        fields: []
      };
    });
    
    // Create the interceptors
    interceptor = new IndexPatternInterceptor(indexPatternService);
    baseInterceptor = new BaseSearchInterceptor();
    
    // Set up the chain
    interceptor.setNext(baseInterceptor);
  });
  
  test('should add index pattern to request', async () => {
    // Create a dataset
    const dataset: Dataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'sample',
      timeFieldName: '@timestamp'
    };
    
    // Create a search request
    const request: SearchRequest = {
      query: {
        query: 'test',
        language: 'kuery',
        dataset
      }
    };
    
    // Execute the search
    const response = await lastValueFrom(interceptor.search(request, {}));
    
    // Verify that getOrCreatePattern was called
    expect(indexPatternService.getOrCreatePattern).toHaveBeenCalledWith(dataset);
    
    // Verify that the response has the index pattern information
    expect(response).toHaveProperty('meta');
    expect(response.meta).toHaveProperty('indexPattern', 'test-dataset');
  });
  
  test('should handle requests without dataset', async () => {
    // Create a search request without a dataset
    const request: SearchRequest = {
      query: {
        query: 'test',
        language: 'kuery'
      }
    };
    
    // Execute the search
    const response = await lastValueFrom(interceptor.search(request, {}));
    
    // Verify that getOrCreatePattern was not called
    expect(indexPatternService.getOrCreatePattern).not.toHaveBeenCalled();
    
    // Verify that the search was executed
    expect(response).toHaveProperty('hits');
  });
  
  test('should handle errors in getOrCreatePattern', async () => {
    // Mock an error in getOrCreatePattern
    indexPatternService.getOrCreatePattern = jest.fn().mockRejectedValue(new Error('Test error'));
    
    // Create a dataset
    const dataset: Dataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'sample'
    };
    
    // Create a search request
    const request: SearchRequest = {
      query: {
        query: 'test',
        language: 'kuery',
        dataset
      }
    };
    
    // Execute the search
    const response = await lastValueFrom(interceptor.search(request, {}));
    
    // Verify that getOrCreatePattern was called
    expect(indexPatternService.getOrCreatePattern).toHaveBeenCalledWith(dataset);
    
    // Verify that the search was executed despite the error
    expect(response).toHaveProperty('hits');
  });
});