import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>ategory, AppError } from '../ErrorHandler';

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler;
  
  beforeEach(() => {
    // Get a fresh instance for each test
    // @ts-expect-error - accessing private constructor for testing
    errorHandler = new ErrorHandler();
  });
  
  it('should categorize network errors correctly', () => {
    const networkError = new Error('Network Error: Failed to fetch');
    const errorInfo = errorHandler.handleError(networkError);
    
    expect(errorInfo.category).toBe(ErrorCategory.NETWORK);
    expect(errorInfo.message).toBe('Network Error: Failed to fetch');
  });
  
  it('should categorize authentication errors correctly', () => {
    const authError = new Error('401 Unauthorized: Invalid credentials');
    const errorInfo = errorHandler.handleError(authError);
    
    expect(errorInfo.category).toBe(ErrorCategory.AUTHENTICATION);
    expect(errorInfo.message).toBe('401 Unauthorized: Invalid credentials');
  });
  
  it('should categorize authorization errors correctly', () => {
    const permissionError = new Error('403 Forbidden: Insufficient permissions');
    const errorInfo = errorHandler.handleError(permissionError);
    
    expect(errorInfo.category).toBe(ErrorCategory.AUTHORIZATION);
    expect(errorInfo.message).toBe('403 Forbidden: Insufficient permissions');
  });
  
  it('should categorize validation errors correctly', () => {
    const validationError = new Error('Validation error: Field is required');
    const errorInfo = errorHandler.handleError(validationError);
    
    expect(errorInfo.category).toBe(ErrorCategory.VALIDATION);
    expect(errorInfo.message).toBe('Validation error: Field is required');
  });
  
  it('should categorize server errors correctly', () => {
    const serverError = new Error('500 Internal Server Error');
    const errorInfo = errorHandler.handleError(serverError);
    
    expect(errorInfo.category).toBe(ErrorCategory.SERVER);
    expect(errorInfo.message).toBe('500 Internal Server Error');
  });
  
  it('should categorize client errors correctly', () => {
    const clientError = new TypeError('Cannot read property of undefined');
    const errorInfo = errorHandler.handleError(clientError);
    
    expect(errorInfo.category).toBe(ErrorCategory.CLIENT);
    expect(errorInfo.message).toBe('Cannot read property of undefined');
  });
  
  it('should handle string errors', () => {
    const stringError = 'Something went wrong';
    const errorInfo = errorHandler.handleError(stringError);
    
    expect(errorInfo.category).toBe(ErrorCategory.UNKNOWN);
    expect(errorInfo.message).toBe('Something went wrong');
  });
  
  it('should handle unknown error types', () => {
    const unknownError = { foo: 'bar' };
    const errorInfo = errorHandler.handleError(unknownError);
    
    expect(errorInfo.category).toBe(ErrorCategory.UNKNOWN);
    expect(errorInfo.message).toBe('An unknown error occurred');
    expect(errorInfo.details?.error).toBe(unknownError);
  });
  
  it('should notify listeners when an error occurs', () => {
    const listener = jest.fn();
    errorHandler.addListener(listener);
    
    const error = new Error('Test error');
    errorHandler.handleError(error);
    
    expect(listener).toHaveBeenCalledWith(expect.objectContaining({
      message: 'Test error',
      originalError: error
    }));
  });
  
  it('should allow removing listeners', () => {
    const listener = jest.fn();
    const removeListener = errorHandler.addListener(listener);
    
    // Remove the listener
    removeListener();
    
    // Handle an error
    errorHandler.handleError(new Error('Test error'));
    
    // The listener should not have been called
    expect(listener).not.toHaveBeenCalled();
  });
  
  it('should create AppError instances', () => {
    const errorInfo = {
      message: 'Test error',
      category: ErrorCategory.CLIENT,
      code: 'TEST_ERROR',
      details: { foo: 'bar' }
    };
    
    const appError = errorHandler.createError(errorInfo);
    
    expect(appError).toBeInstanceOf(AppError);
    expect(appError.message).toBe('Test error');
    expect(appError.category).toBe(ErrorCategory.CLIENT);
    expect(appError.code).toBe('TEST_ERROR');
    expect(appError.details).toEqual({ foo: 'bar' });
  });
});