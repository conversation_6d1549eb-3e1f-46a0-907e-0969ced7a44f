import { SearchRequest, SearchResponse } from '../interfaces';

/**
 * Interface for cached search results
 */
interface CachedSearchResult {
  response: SearchResponse;
  timestamp: number;
  expiresAt: number;
  hitCount: number; // Track how many times this cache entry has been accessed
}

/**
 * Service for caching search results to improve performance
 */
export class QueryCacheService {
  private cache: Map<string, CachedSearchResult> = new Map();
  private readonly DEFAULT_CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of cached queries
  private readonly ADAPTIVE_EXPIRATION = true; // Enable adaptive expiration based on hit count
  
  /**
   * Gets a cached search result for a request
   * @param request The search request
   * @returns The cached search response or undefined if not found
   */
  public get(request: SearchRequest): SearchResponse | undefined {
    const key = this.generateCacheKey(request);
    const cachedEntry = this.cache.get(key);
    
    if (!cachedEntry) {
      return undefined;
    }
    
    // Check if cache has expired
    if (Date.now() > cachedEntry.expiresAt) {
      this.cache.delete(key);
      return undefined;
    }
    
    // Update hit count and potentially extend expiration for frequently accessed items
    cachedEntry.hitCount++;
    
    if (this.ADAPTIVE_EXPIRATION && cachedEntry.hitCount > 5) {
      // Extend expiration time for frequently accessed items
      const extensionFactor = Math.min(cachedEntry.hitCount / 5, 3); // Cap at 3x extension
      const now = Date.now();
      const originalExpiration = cachedEntry.expiresAt - cachedEntry.timestamp;
      const newExpiration = originalExpiration * extensionFactor;
      
      cachedEntry.expiresAt = now + newExpiration;
    }
    
    return cachedEntry.response;
  }
  
  /**
   * Sets a search result in the cache
   * @param request The search request
   * @param response The search response
   * @param expiration Optional expiration time in milliseconds
   */
  public set(
    request: SearchRequest, 
    response: SearchResponse, 
    expiration: number = this.DEFAULT_CACHE_EXPIRATION
  ): void {
    // Ensure the cache doesn't grow too large
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastValuableEntry();
    }
    
    const key = this.generateCacheKey(request);
    const now = Date.now();
    
    // Check if we're updating an existing entry
    const existing = this.cache.get(key);
    
    this.cache.set(key, {
      response,
      timestamp: now,
      expiresAt: now + expiration,
      hitCount: existing ? existing.hitCount + 1 : 1
    });
  }
  
  /**
   * Checks if a request is cached
   * @param request The search request
   * @returns True if the request is cached and not expired
   */
  public has(request: SearchRequest): boolean {
    const key = this.generateCacheKey(request);
    const cached = this.cache.get(key);
    
    if (!cached) {
      return false;
    }
    
    // Check if cache has expired
    if (Date.now() > cached.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
  
  /**
   * Invalidates the cache for a specific dataset
   * @param datasetId The dataset ID
   */
  public invalidateForDataset(datasetId: string): void {
    for (const [key] of this.cache.entries()) {
      if (key.includes(`"index":"${datasetId}"`)) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * Clears the entire cache
   */
  public clear(): void {
    this.cache.clear();
  }
  
  /**
   * Gets the number of cached entries
   * @returns The cache size
   */
  public size(): number {
    return this.cache.size;
  }
  
  /**
   * Generates a cache key for a search request
   * @param request The search request
   * @returns The cache key
   */
  private generateCacheKey(request: SearchRequest): string {
    // Create a normalized version of the request for consistent keys
    const normalizedRequest = {
      query: request.query,
      index: request.index,
      size: request.size,
      from: request.from,
      sort: request.sort,
      fields: request.fields,
      // Exclude highlight as it doesn't affect the results, only the presentation
    };
    
    return JSON.stringify(normalizedRequest);
  }
  
  /**
   * Evicts the least valuable entry from the cache based on a combination of age and hit count
   */
  private evictLeastValuableEntry(): void {
    let leastValuableKey: string | null = null;
    let lowestValue = Infinity;
    const now = Date.now();
    
    for (const [key, cached] of this.cache.entries()) {
      // Calculate a value score based on recency and hit count
      // Higher hit count and more recent entries are more valuable
      const age = now - cached.timestamp;
      const ageScore = age / this.DEFAULT_CACHE_EXPIRATION; // Normalize age
      const hitScore = 1 / (cached.hitCount + 1); // Inverse of hit count (lower is better)
      
      // Combined score - lower is less valuable
      const value = hitScore * 0.7 + ageScore * 0.3; // Weight hit count more than age
      
      if (value < lowestValue) {
        lowestValue = value;
        leastValuableKey = key;
      }
    }
    
    if (leastValuableKey) {
      this.cache.delete(leastValuableKey);
    }
  }
  
  /**
   * Gets cache statistics for monitoring
   * @returns Object with cache statistics
   */
  public getStats(): { size: number; hitRate: number; avgHitCount: number } {
    const size = this.cache.size;
    let totalHits = 0;
    
    for (const cached of this.cache.values()) {
      totalHits += cached.hitCount;
    }
    
    const avgHitCount = size > 0 ? totalHits / size : 0;
    
    return {
      size,
      hitRate: this._hitCount / (this._hitCount + this._missCount || 1),
      avgHitCount
    };
  }
  
  // Hit/miss tracking for statistics
  private _hitCount = 0;
  private _missCount = 0;
  
  /**
   * Resets the hit/miss statistics
   */
  public resetStats(): void {
    this._hitCount = 0;
    this._missCount = 0;
  }
}