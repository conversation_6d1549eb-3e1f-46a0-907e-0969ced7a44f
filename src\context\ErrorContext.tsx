import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { errorH<PERSON><PERSON>, ErrorInfo, ErrorCategory } from '../services/ErrorHandler';
import { useToast } from './ToastContext';

interface ErrorContextType {
  handleError: (error: unknown) => ErrorInfo;
}

// Create the context
const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
}

/**
 * Provider component for the ErrorContext
 */
export const ErrorProvider: React.FC<ErrorProviderProps> = ({ children }) => {
  const { addToast } = useToast();
  
  // Add a global error listener
  useEffect(() => {
    const removeListener = errorHandler.addListener((errorInfo) => {
      // Show a toast notification for the error
      const message = getErrorMessage(errorInfo);
      addToast(message, 'error');
      
      // Log the error to the console
      console.error('Error handled by <PERSON><PERSON>r<PERSON>ontext:', errorInfo);
    });
    
    return removeListener;
  }, [addToast]);
  
  // Get a user-friendly error message based on the error category
  const getErrorMessage = (errorInfo: ErrorInfo): string => {
    switch (errorInfo.category) {
      case ErrorCategory.NETWORK:
        return 'Network error: Unable to connect to the server. Please check your internet connection.';
      
      case ErrorCategory.AUTHENTICATION:
        return 'Authentication error: Please log in and try again.';
      
      case ErrorCategory.AUTHORIZATION:
        return 'Authorization error: You do not have permission to perform this action.';
      
      case ErrorCategory.VALIDATION:
        return `Validation error: ${errorInfo.message}`;
      
      case ErrorCategory.SERVER:
        return 'Server error: The server encountered an error. Please try again later.';
      
      case ErrorCategory.CLIENT:
        return 'An error occurred in the application. Please try again.';
      
      case ErrorCategory.UNKNOWN:
      default:
        return errorInfo.message || 'An unknown error occurred.';
    }
  };
  
  // Create the context value
  const contextValue: ErrorContextType = {
    handleError: errorHandler.handleError.bind(errorHandler)
  };
  
  return (
    <ErrorContext.Provider value={contextValue}>
      {children}
    </ErrorContext.Provider>
  );
};

/**
 * Hook for using the ErrorContext
 */
export const useError = (): ErrorContextType => {
  const context = useContext(ErrorContext);
  
  if (!context) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  
  return context;
};