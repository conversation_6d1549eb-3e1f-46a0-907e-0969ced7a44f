import React, { useState } from 'react';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';

/**
 * Component for discover-specific filters
 * Provides filters for log levels, sources, and other common fields
 */
const DiscoverFilters: React.FC = () => {
    const { state, dispatch } = useDiscover();
    const { filteredData, appliedFilters } = state;

    // State for expanded filter sections
    const [expandedSections, setExpandedSections] = useState({
        level: true,
        source: true,
        rule: true
    });

    // Toggle section expansion
    const toggleSection = (section: 'level' | 'source' | 'rule') => {
        setExpandedSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    // Check if a filter is applied
    const isFilterApplied = (field: string, value: unknown): boolean => {
        return appliedFilters.some(filter =>
            filter.field === field &&
            (filter.value === value ||
                (Array.isArray(filter.value) && filter.value.includes(value)))
        );
    };

    // Toggle a filter
    const toggleFilter = (field: string, value: unknown) => {
        if (isFilterApplied(field, value)) {
            dispatch(discoverActions.removeFilter(field));
        } else {
            dispatch(discoverActions.addFilter(field, value));
        }
    };

    // Get log level color
    const getLevelColor = (level: string): string => {
        switch (level.toLowerCase()) {
            case 'error':
                return '#F44336'; // Red
            case 'warning':
                return '#FF9800'; // Orange
            case 'info':
                return '#2196F3'; // Blue
            case 'debug':
                return '#4CAF50'; // Green
            default:
                return '#9E9E9E'; // Gray
        }
    };

    // Calculate distribution of log levels
    const levelDistribution = React.useMemo(() => {
        const distribution: Record<string, number> = {};

        filteredData.forEach(log => {
            const level = log.level || 'unknown';
            distribution[level] = (distribution[level] || 0) + 1;
        });

        return distribution;
    }, [filteredData]);

    // Calculate distribution of sources
    const sourceDistribution = React.useMemo(() => {
        const distribution: Record<string, number> = {};

        filteredData.forEach(log => {
            const source = log.source || 'unknown';
            distribution[source] = (distribution[source] || 0) + 1;
        });

        return distribution;
    }, [filteredData]);

    // Calculate distribution of rule types
    const ruleDistribution = React.useMemo(() => {
        const distribution: Record<string, number> = {};

        filteredData.forEach(log => {
            if (log.rule && log.rule.type) {
                const ruleType = log.rule.type;
                distribution[ruleType] = (distribution[ruleType] || 0) + 1;
            }
        });

        return distribution;
    }, [filteredData]);

    return (
        <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
        }}>
            {/* Log Level Filter */}
            <div style={{
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
                overflow: 'hidden',
            }}>
                <div
                    onClick={() => toggleSection('level')}
                    style={{
                        padding: '12px 16px',
                        background: 'rgba(0, 229, 255, 0.05)',
                        cursor: 'pointer',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <h4 style={{ margin: 0, fontSize: '14px', color: 'white' }}>
                        Log Level
                    </h4>
                    <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        style={{
                            transform: expandedSections.level ? 'rotate(180deg)' : 'rotate(0deg)',
                            transition: 'transform 0.2s',
                            color: 'rgba(255, 255, 255, 0.7)',
                        }}
                    >
                        <polyline points="6 9 12 15 18 9" />
                    </svg>
                </div>

                {expandedSections.level && (
                    <div style={{ padding: '8px 16px 16px' }}>
                        {Object.entries(levelDistribution).map(([level, count]) => (
                            <div
                                key={level}
                                onClick={() => toggleFilter('level', level)}
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    padding: '8px 0',
                                    cursor: 'pointer',
                                }}
                            >
                                <div style={{
                                    width: '16px',
                                    height: '16px',
                                    borderRadius: '2px',
                                    border: '1px solid rgba(255, 255, 255, 0.3)',
                                    background: isFilterApplied('level', level) ? 'rgba(0, 229, 255, 0.2)' : 'transparent',
                                    marginRight: '8px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                    {isFilterApplied('level', level) && (
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                                            <polyline points="20 6 9 17 4 12" />
                                        </svg>
                                    )}
                                </div>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    flex: 1,
                                }}>
                                    <div style={{ display: 'flex', alignItems: 'center' }}>
                                        <div style={{
                                            width: '12px',
                                            height: '12px',
                                            borderRadius: '50%',
                                            background: getLevelColor(level),
                                            marginRight: '8px',
                                        }} />
                                        <span style={{ color: 'white', textTransform: 'capitalize' }}>
                                            {level}
                                        </span>
                                    </div>
                                    <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                                        {count}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Source Filter */}
            <div style={{
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
                overflow: 'hidden',
            }}>
                <div
                    onClick={() => toggleSection('source')}
                    style={{
                        padding: '12px 16px',
                        background: 'rgba(0, 229, 255, 0.05)',
                        cursor: 'pointer',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <h4 style={{ margin: 0, fontSize: '14px', color: 'white' }}>
                        Source
                    </h4>
                    <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        style={{
                            transform: expandedSections.source ? 'rotate(180deg)' : 'rotate(0deg)',
                            transition: 'transform 0.2s',
                            color: 'rgba(255, 255, 255, 0.7)',
                        }}
                    >
                        <polyline points="6 9 12 15 18 9" />
                    </svg>
                </div>

                {expandedSections.source && (
                    <div style={{ padding: '8px 16px 16px' }}>
                        {Object.entries(sourceDistribution).map(([source, count]) => (
                            <div
                                key={source}
                                onClick={() => toggleFilter('source', source)}
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    padding: '8px 0',
                                    cursor: 'pointer',
                                }}
                            >
                                <div style={{
                                    width: '16px',
                                    height: '16px',
                                    borderRadius: '2px',
                                    border: '1px solid rgba(255, 255, 255, 0.3)',
                                    background: isFilterApplied('source', source) ? 'rgba(0, 229, 255, 0.2)' : 'transparent',
                                    marginRight: '8px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                    {isFilterApplied('source', source) && (
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                                            <polyline points="20 6 9 17 4 12" />
                                        </svg>
                                    )}
                                </div>
                                <div style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    flex: 1,
                                }}>
                                    <span style={{ color: 'white' }}>
                                        {source}
                                    </span>
                                    <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                                        {count}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Rule Type Filter */}
            {Object.keys(ruleDistribution).length > 0 && (
                <div style={{
                    background: 'rgba(0, 0, 0, 0.2)',
                    borderRadius: '4px',
                    overflow: 'hidden',
                }}>
                    <div
                        onClick={() => toggleSection('rule')}
                        style={{
                            padding: '12px 16px',
                            background: 'rgba(0, 229, 255, 0.05)',
                            cursor: 'pointer',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        }}
                    >
                        <h4 style={{ margin: 0, fontSize: '14px', color: 'white' }}>
                            Rule Type
                        </h4>
                        <svg
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            style={{
                                transform: expandedSections.rule ? 'rotate(180deg)' : 'rotate(0deg)',
                                transition: 'transform 0.2s',
                                color: 'rgba(255, 255, 255, 0.7)',
                            }}
                        >
                            <polyline points="6 9 12 15 18 9" />
                        </svg>
                    </div>

                    {expandedSections.rule && (
                        <div style={{ padding: '8px 16px 16px' }}>
                            {Object.entries(ruleDistribution).map(([ruleType, count]) => (
                                <div
                                    key={ruleType}
                                    onClick={() => toggleFilter('rule.type', ruleType)}
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        padding: '8px 0',
                                        cursor: 'pointer',
                                    }}
                                >
                                    <div style={{
                                        width: '16px',
                                        height: '16px',
                                        borderRadius: '2px',
                                        border: '1px solid rgba(255, 255, 255, 0.3)',
                                        background: isFilterApplied('rule.type', ruleType) ? 'rgba(0, 229, 255, 0.2)' : 'transparent',
                                        marginRight: '8px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                        {isFilterApplied('rule.type', ruleType) && (
                                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                                                <polyline points="20 6 9 17 4 12" />
                                            </svg>
                                        )}
                                    </div>
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        flex: 1,
                                    }}>
                                        <span style={{ color: 'white' }}>
                                            {ruleType}
                                        </span>
                                        <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                                            {count}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default DiscoverFilters;