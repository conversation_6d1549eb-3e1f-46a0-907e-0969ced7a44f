import { SampleSearchStrategy } from '../sample-search-strategy';
import { DataFrameTransformer } from '../data-frame-transformer';
import { SearchRequest } from '../../interfaces';

describe('Search Strategies', () => {
  describe('SampleSearchStrategy', () => {
    let strategy: SampleSearchStrategy;
    
    beforeEach(() => {
      strategy = new SampleSearchStrategy();
    });
    
    test('should return sample data', async () => {
      const request: SearchRequest = {
        query: { query: 'test', language: 'kuery' },
        size: 5
      };
      
      const response = await strategy.search(request);
      
      expect(response).toHaveProperty('hits');
      expect(response.hits).toHaveProperty('total', 100);
      expect(response.hits.hits).toHaveLength(5);
      expect(response).toHaveProperty('took');
      expect(response).toHaveProperty('timed_out', false);
    });
    
    test('should handle errors gracefully', async () => {
      const request: SearchRequest = {
        query: { query: 'trigger error', language: 'kuery' }
      };
      
      const response = await strategy.search(request);
      
      expect(response).toHaveProperty('hits');
      expect(response.hits).toHaveProperty('total', 0);
      expect(response.hits.hits).toHaveLength(0);
      expect(response).toHaveProperty('timed_out', true);
      expect(response).toHaveProperty('error');
      expect(response.error).toHaveProperty('message', 'Simulated error in sample search strategy');
    });
    
    test('should respect size and from parameters', async () => {
      const request: SearchRequest = {
        query: { query: 'test', language: 'kuery' },
        size: 3,
        from: 10
      };
      
      const response = await strategy.search(request);
      
      expect(response.hits.hits).toHaveLength(3);
      expect(response.hits.hits[0]._id).toBe('doc_10');
      expect(response.hits.hits[1]._id).toBe('doc_11');
      expect(response.hits.hits[2]._id).toBe('doc_12');
    });
  });
  
  describe('DataFrameTransformer', () => {
    test('should transform search response to DataFrame', () => {
      const response = {
        hits: {
          total: 2,
          hits: [
            {
              _id: 'doc_1',
              _source: {
                timestamp: '2023-01-01T12:00:00Z',
                message: 'Test message 1',
                level: 'info',
                count: 42
              }
            },
            {
              _id: 'doc_2',
              _source: {
                timestamp: '2023-01-01T12:01:00Z',
                message: 'Test message 2',
                level: 'error',
                count: 43
              }
            }
          ]
        },
        took: 5,
        timed_out: false
      };
      
      const dataFrame = DataFrameTransformer.transform(response);
      
      expect(dataFrame).toHaveProperty('columns');
      expect(dataFrame).toHaveProperty('rows');
      expect(dataFrame).toHaveProperty('meta');
      
      // Check columns
      expect(dataFrame.columns).toHaveLength(5); // _id, timestamp, message, level, count
      expect(dataFrame.columns.map(c => c.id)).toContain('_id');
      expect(dataFrame.columns.map(c => c.id)).toContain('timestamp');
      expect(dataFrame.columns.map(c => c.id)).toContain('message');
      expect(dataFrame.columns.map(c => c.id)).toContain('level');
      expect(dataFrame.columns.map(c => c.id)).toContain('count');
      
      // Check rows
      expect(dataFrame.rows).toHaveLength(2);
      expect(dataFrame.rows[0]).toHaveProperty('_id', 'doc_1');
      expect(dataFrame.rows[0]).toHaveProperty('message', 'Test message 1');
      expect(dataFrame.rows[0]).toHaveProperty('level', 'info');
      expect(dataFrame.rows[0]).toHaveProperty('count', 42);
      
      expect(dataFrame.rows[1]).toHaveProperty('_id', 'doc_2');
      expect(dataFrame.rows[1]).toHaveProperty('message', 'Test message 2');
      expect(dataFrame.rows[1]).toHaveProperty('level', 'error');
      expect(dataFrame.rows[1]).toHaveProperty('count', 43);
      
      // Check meta
      expect(dataFrame.meta).toHaveProperty('total', 2);
      expect(dataFrame.meta).toHaveProperty('took', 5);
    });
    
    test('should handle empty response', () => {
      const response = {
        hits: {
          total: 0,
          hits: []
        },
        took: 5,
        timed_out: false
      };
      
      const dataFrame = DataFrameTransformer.transform(response);
      
      expect(dataFrame.columns).toHaveLength(0);
      expect(dataFrame.rows).toHaveLength(0);
      expect(dataFrame.meta).toHaveProperty('total', 0);
      expect(dataFrame.meta).toHaveProperty('took', 5);
    });
    
    test('should handle invalid response', () => {
      const response = {
        took: 5,
        timed_out: false
      } as unknown;
      
      const dataFrame = DataFrameTransformer.transform(response);
      
      expect(dataFrame.columns).toHaveLength(0);
      expect(dataFrame.rows).toHaveLength(0);
      expect(dataFrame.meta).toHaveProperty('total', 0);
      expect(dataFrame.meta).toHaveProperty('took', 5);
    });
  });
});