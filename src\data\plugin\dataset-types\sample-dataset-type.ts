import { DatasetTypeConfig, Dataset, DatasetField, DataStructure } from '../interfaces';

/**
 * Sample dataset type implementation
 * This demonstrates how to create a dataset type for the DatasetService
 */
export const sampleDatasetType: DatasetTypeConfig = {
  id: 'sample-data',
  title: 'Sample Data',
  meta: {
    icon: 'database', // This would be a component or icon reference in a real implementation
    tooltip: 'Sample data for testing and demonstration',
    searchOnLoad: true,
    isFieldLoadAsync: false
  },
  
  /**
   * Converts a path to a dataset
   * @param path The path to convert
   * @returns The created dataset
   */
  toDataset: (path: DataStructure[]): Dataset => {
    if (!path || path.length === 0) {
      throw new Error('Invalid path');
    }
    
    const lastItem = path[path.length - 1];
    
    return {
      id: `sample-${lastItem.id}`,
      title: lastItem.title,
      type: 'sample-data',
      timeFieldName: '@timestamp',
      language: 'kuery'
    };
  },
  
  /**
   * Fetches data structure for the dataset
   * @param services Services needed for fetching
   * @param path The path to fetch
   * @returns The fetched data structure
   */
  fetch: async (services: unknown, path: DataStructure[]): Promise<DataStructure> => {
    // In a real implementation, this would fetch data from a source
    // For this example, we'll just return a mock structure
    
    const lastItem = path.length > 0 ? path[path.length - 1] : null;
    
    if (!lastItem) {
      return {
        id: 'root',
        title: 'Sample Data',
        type: 'folder',
        children: [
          {
            id: 'logs',
            title: 'Logs',
            type: 'dataset'
          },
          {
            id: 'metrics',
            title: 'Metrics',
            type: 'dataset'
          }
        ]
      };
    }
    
    // Return details for the specific item
    return {
      id: lastItem.id,
      title: lastItem.title,
      type: 'dataset',
      meta: {
        count: 1000,
        lastUpdated: new Date().toISOString()
      }
    };
  },
  
  /**
   * Fetches fields for a dataset
   * @param dataset The dataset to fetch fields for
   * @returns Array of dataset fields
   */
  fetchFields: async (dataset: Dataset): Promise<DatasetField[]> => {
    // In a real implementation, this would fetch fields from the dataset
    // For this example, we'll return mock fields based on the dataset ID
    
    const commonFields: DatasetField[] = [
      {
        name: '@timestamp',
        type: 'date',
        searchable: true,
        aggregatable: true,
        format: {
          id: 'date',
          params: {
            pattern: 'YYYY-MM-DD HH:mm:ss'
          }
        }
      },
      {
        name: 'host.name',
        type: 'keyword',
        searchable: true,
        aggregatable: true
      }
    ];
    
    // Add specific fields based on dataset ID
    if (dataset.id.includes('logs')) {
      return [
        ...commonFields,
        {
          name: 'message',
          type: 'text',
          searchable: true,
          aggregatable: false
        },
        {
          name: 'log.level',
          type: 'keyword',
          searchable: true,
          aggregatable: true
        }
      ];
    } else if (dataset.id.includes('metrics')) {
      return [
        ...commonFields,
        {
          name: 'system.cpu.usage',
          type: 'float',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'system.memory.used',
          type: 'float',
          searchable: true,
          aggregatable: true
        }
      ];
    }
    
    return commonFields;
  },
  
  /**
   * Returns the supported query languages for this dataset type
   * @returns Array of supported language IDs
   */
  supportedLanguages: (): string[] => {
    return ['kuery', 'lucene'];
  }
};