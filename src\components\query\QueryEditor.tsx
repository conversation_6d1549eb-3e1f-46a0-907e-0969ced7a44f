import React, { useState, useEffect, useRef } from 'react';
import { useQuery, useDataset } from '../../hooks/dataHooks';
import { useLanguageAutocomplete } from '../../hooks/uiHooks';
import { Query, Completion } from '../../data/plugin/interfaces';

interface QueryEditorProps {
  initialQuery?: string;
  initialLanguage?: string;
  onSubmit?: (query: Query) => void;
  className?: string;
  placeholder?: string;
  autoFocus?: boolean;
}

/**
 * Language-aware query editor component
 */
const QueryEditor: React.FC<QueryEditorProps> = ({
  initialQuery = '',
  initialLanguage = 'kuery',
  onSubmit,
  className = '',
  placeholder = 'Enter a query...',
  autoFocus = false
}) => {
  const { query, setQuery } = useQuery();
  const { selectedDataset } = useDataset();
  const [inputValue, setInputValue] = useState(initialQuery || query.query);
  const [language, setLanguage] = useState(initialLanguage || query.language);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<Completion[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [highlightedQuery, setHighlightedQuery] = useState<string>('');
  
  // Get language-specific autocomplete
  const { 
    getCompletions, 
    validateQuery, 
    highlightQuery, 
    hasAutocomplete 
  } = useLanguageAutocomplete(language);
  
  // Refs
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  
  // Get available languages for the selected dataset
  const availableLanguages = selectedDataset?.supportedLanguages || ['kuery', 'lucene'];
  
  // Update input value when query changes
  useEffect(() => {
    if (query.query !== inputValue) {
      setInputValue(query.query);
    }
  }, [query.query, inputValue]);
  
  // Update language when query language changes
  useEffect(() => {
    if (query.language !== language) {
      setLanguage(query.language);
    }
  }, [query.language, language]);
  
  // Highlight query when input value or language changes
  useEffect(() => {
    setHighlightedQuery(highlightQuery(inputValue));
  }, [inputValue, language, highlightQuery]);
  
  // Validate query when input value or language changes
  useEffect(() => {
    const validation = validateQuery(inputValue);
    setValidationError(validation.valid ? null : validation.error || 'Invalid query');
  }, [inputValue, language, validateQuery]);
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    // Update cursor position
    if (inputRef.current) {
      setCursorPosition(inputRef.current.selectionStart || 0);
    }
    
    // Show suggestions if autocomplete is available
    if (hasAutocomplete) {
      updateSuggestions(value);
    }
  };
  
  // Handle cursor position change
  const handleCursorPositionChange = () => {
    if (inputRef.current) {
      setCursorPosition(inputRef.current.selectionStart || 0);
      
      // Update suggestions based on new cursor position
      if (hasAutocomplete && showSuggestions) {
        updateSuggestions(inputValue);
      }
    }
  };
  
  // Update suggestions based on input value and cursor position
  const updateSuggestions = async (value: string) => {
    if (!hasAutocomplete) return;
    
    try {
      const completions = await getCompletions(value, cursorPosition);
      
      if (completions.length > 0) {
        setSuggestions(completions);
        setShowSuggestions(true);
        setSelectedSuggestion(0);
      } else {
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error('Error getting completions:', error);
      setShowSuggestions(false);
    }
  };
  
  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: Completion) => {
    // Insert the suggestion at the cursor position
    const before = inputValue.substring(0, cursorPosition);
    const after = inputValue.substring(cursorPosition);
    
    // Find the word boundary before the cursor
    let wordStart = before.length;
    for (let i = before.length - 1; i >= 0; i--) {
      if (before[i] === ' ' || before[i] === '(' || before[i] === ':') {
        wordStart = i + 1;
        break;
      }
    }
    
    // Replace the word at the cursor with the suggestion
    const newValue = before.substring(0, wordStart) + suggestion.value + after;
    setInputValue(newValue);
    
    // Update cursor position
    const newCursorPosition = wordStart + suggestion.value.length;
    setCursorPosition(newCursorPosition);
    
    // Focus the input and set the cursor position
    if (inputRef.current) {
      inputRef.current.focus();
      inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
    }
    
    // Hide suggestions
    setShowSuggestions(false);
  };
  
  // Handle key down events
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle suggestion navigation
    if (showSuggestions) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestion((prev) => 
            prev < suggestions.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestion((prev) => (prev > 0 ? prev - 1 : 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (suggestions[selectedSuggestion]) {
            handleSuggestionSelect(suggestions[selectedSuggestion]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowSuggestions(false);
          break;
      }
    } else if (e.key === 'Enter') {
      // Submit query on Enter
      handleSubmit();
    }
  };
  
  // Handle form submit
  const handleSubmit = () => {
    // Update the query
    const newQuery: Query = {
      query: inputValue,
      language,
      dataset: selectedDataset
    };
    
    setQuery(newQuery);
    
    // Call onSubmit callback if provided
    if (onSubmit) {
      onSubmit(newQuery);
    }
  };
  
  // Handle language change
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = e.target.value;
    setLanguage(newLanguage);
    
    // Update the query
    setQuery({
      ...query,
      language: newLanguage
    });
  };
  
  // Handle click outside suggestions
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(e.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(e.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className={`query-editor ${className}`}>
      <div className="query-editor-container">
        <div className="query-input-container">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onClick={handleCursorPositionChange}
            onFocus={() => hasAutocomplete && updateSuggestions(inputValue)}
            placeholder={placeholder}
            className={`query-input ${validationError ? 'invalid' : ''}`}
            autoFocus={autoFocus}
          />
          
          {showSuggestions && suggestions.length > 0 && (
            <div ref={suggestionsRef} className="query-suggestions">
              {suggestions.map((suggestion, index) => (
                <div
                  key={`${suggestion.value}-${index}`}
                  className={`suggestion-item ${index === selectedSuggestion ? 'selected' : ''}`}
                  onClick={() => handleSuggestionSelect(suggestion)}
                >
                  <span className="suggestion-value">{suggestion.value}</span>
                  {suggestion.meta && (
                    <span className="suggestion-meta">{suggestion.meta}</span>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="query-language-selector">
          <select
            value={language}
            onChange={handleLanguageChange}
            className="language-select"
          >
            {availableLanguages.map((lang) => (
              <option key={lang} value={lang}>
                {lang.toUpperCase()}
              </option>
            ))}
          </select>
        </div>
        
        <button
          onClick={handleSubmit}
          className="query-submit-button"
          disabled={!!validationError}
        >
          Search
        </button>
      </div>
      
      {validationError && (
        <div className="query-validation-error">
          {validationError}
        </div>
      )}
      
      <div 
        className="query-highlight"
        dangerouslySetInnerHTML={{ __html: highlightedQuery }}
      />
    </div>
  );
};

export default QueryEditor;