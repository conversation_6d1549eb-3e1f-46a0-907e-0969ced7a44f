import { SearchServiceImpl } from '../search-service';
import { IndexPatternService } from '../index-pattern-service';
import { Dataset } from '../../interfaces';

// Mock the index pattern service
jest.mock('../index-pattern-service');

describe('SearchService', () => {
  let searchService: SearchServiceImpl;
  let indexPatternService: jest.Mocked<IndexPatternService>;
  
  beforeEach(() => {
    // Create a mock index pattern service
    indexPatternService = new IndexPatternService() as jest.Mocked<IndexPatternService>;
    
    // Mock the getOrCreatePattern method
    indexPatternService.getOrCreatePattern = jest.fn().mockImplementation(async (dataset) => {
      return {
        id: `temp_${dataset.id}`,
        title: dataset.title,
        timeFieldName: dataset.timeFieldName,
        fields: []
      };
    });
    
    // Create the search service
    searchService = new SearchServiceImpl(indexPatternService);
  });
  
  test('should create a search source', async () => {
    const searchSource = await searchService.searchSource.create();
    expect(searchSource).toBeDefined();
    expect(searchSource.setField).toBeDefined();
    expect(searchSource.getField).toBeDefined();
    expect(searchSource.fetch).toBeDefined();
  });
  
  test('should execute a search with a dataset', async () => {
    // Create a dataset
    const dataset: Dataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'sample',
      timeFieldName: '@timestamp'
    };
    
    // Create a search source
    const searchSource = await searchService.searchSource.create();
    
    // Configure the search source
    searchSource.setField('query', {
      query: 'test',
      language: 'kuery',
      dataset
    });
    
    // Execute the search
    const response = await searchSource.fetch();
    
    // Verify that the index pattern service was used
    expect(indexPatternService.getOrCreatePattern).toHaveBeenCalledWith(dataset);
    
    // Verify the response
    expect(response).toHaveProperty('hits');
    expect(response).toHaveProperty('took');
    expect(response).toHaveProperty('timed_out');
    expect(response).toHaveProperty('meta');
    expect(response.meta).toHaveProperty('indexPattern', 'test-dataset');
  });
  
  test('should execute a search without a dataset', async () => {
    // Create a search source
    const searchSource = await searchService.searchSource.create();
    
    // Configure the search source
    searchSource.setField('query', {
      query: 'test',
      language: 'kuery'
    });
    
    // Execute the search
    const response = await searchSource.fetch();
    
    // Verify that the index pattern service was not used
    expect(indexPatternService.getOrCreatePattern).not.toHaveBeenCalled();
    
    // Verify the response
    expect(response).toHaveProperty('hits');
    expect(response).toHaveProperty('took');
    expect(response).toHaveProperty('timed_out');
  });
  
  test('should provide access to the index pattern service', () => {
    const service = searchService.getIndexPatternService();
    expect(service).toBe(indexPatternService);
  });
});