/**
 * Initialize performance monitoring system
 * 
 * This module provides a function to initialize the performance monitoring system
 * and register console commands for accessing performance metrics.
 */

import { registerConsoleCommands } from './performanceLogger';
import { clearMetrics } from './performanceTracking';
import { clearComparisonResults } from './performanceComparison';

/**
 * Initialize performance monitoring system
 * @param enableConsoleCommands Whether to register console commands
 * @param clearExistingMetrics Whether to clear existing metrics
 */
export function initPerformanceMonitoring(
  enableConsoleCommands: boolean = true,
  clearExistingMetrics: boolean = true
): void {
  // Clear existing metrics if requested
  if (clearExistingMetrics) {
    clearMetrics();
    clearComparisonResults();
  }
  
  // Register console commands if requested
  if (enableConsoleCommands) {
    registerConsoleCommands();
  }
  
  // Log initialization
  console.log(
    '%cPerformance monitoring initialized',
    'font-weight: bold; color: #00e5ff;'
  );
  
  // Add performance monitoring to window object for debugging
  if (typeof window !== 'undefined') {
    // Create a custom event for showing the performance monitor
    const showMonitorEvent = new CustomEvent('show-performance-monitor');
    
    (window as Window & typeof globalThis).__performanceMonitoring = {
      enabled: true,
      initialized: Date.now(),
      clearMetrics,
      clearComparisonResults,
      // Method to show the performance monitor
      showMonitor: () => {
        window.dispatchEvent(showMonitorEvent);
        // Also set the hash to enable the monitor
        if (window.location.hash !== '#perf-monitor') {
          window.location.hash = 'perf-monitor';
        }
        console.log('%cPerformance monitor activated', 'font-weight: bold; color: #00e5ff;');
      }
    };
  }
}

/**
 * Check if performance monitoring is enabled
 * @returns Boolean indicating if performance monitoring is enabled
 */
export function isPerformanceMonitoringEnabled(): boolean {
  if (typeof window !== 'undefined') {
    return (window as Window & typeof globalThis).__performanceMonitoring?.enabled === true;
  }
  return false;
}

/**
 * Enable or disable performance monitoring
 * @param enabled Whether to enable performance monitoring
 */
export function setPerformanceMonitoringEnabled(enabled: boolean): void {
  if (typeof window !== 'undefined') {
    if (!(window as Window & typeof globalThis).__performanceMonitoring) {
      initPerformanceMonitoring();
    }
    
    (window as Window & typeof globalThis).__performanceMonitoring.enabled = enabled;
    
    console.log(
      `%cPerformance monitoring ${enabled ? 'enabled' : 'disabled'}`,
      'font-weight: bold; color: #00e5ff;'
    );
  }
}