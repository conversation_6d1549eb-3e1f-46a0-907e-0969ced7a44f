import React, { useState } from 'react';
import { useDataPlugin } from '../../context/DataPluginContext';
import { SearchResponse } from '../../data/plugin/interfaces';

/**
 * Example component that demonstrates how to use the DataPlugin context
 */
const DataPluginExample: React.FC = () => {
  const dataPlugin = useDataPlugin();
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Example of using the search service
  const executeSearch = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const searchService = dataPlugin.getSearchService();
      const searchSource = await searchService.searchSource.create();
      
      // Configure the search
      searchSource.setField('query', { query: 'example query', language: 'kuery' });
      searchSource.setField('size', 10);
      
      // Execute the search
      const response = await searchSource.fetch();
      setSearchResults(response);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Example of using the query service
  const updateQuery = () => {
    const queryService = dataPlugin.getQueryService();
    queryService.queryString.setQuery({
      query: 'new example query',
      language: 'kuery'
    });
  };
  
  return (
    <div className="data-plugin-example">
      <h2>Data Plugin Example</h2>
      
      <div className="actions">
        <button onClick={executeSearch} disabled={isLoading}>
          {isLoading ? 'Searching...' : 'Execute Search'}
        </button>
        <button onClick={updateQuery}>Update Query</button>
      </div>
      
      {error && (
        <div className="error">
          <h3>Error</h3>
          <p>{error.message}</p>
        </div>
      )}
      
      {searchResults && (
        <div className="results">
          <h3>Search Results</h3>
          <p>Total hits: {searchResults.hits.total}</p>
          <p>Took: {searchResults.took}ms</p>
          
          {searchResults.hits.hits.length > 0 ? (
            <ul>
              {searchResults.hits.hits.map((hit) => (
                <li key={hit._id}>
                  <pre>{JSON.stringify(hit._source, null, 2)}</pre>
                </li>
              ))}
            </ul>
          ) : (
            <p>No results found</p>
          )}
        </div>
      )}
    </div>
  );
};

export default DataPluginExample;